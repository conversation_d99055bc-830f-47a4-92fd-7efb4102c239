"""
Data handler module for fetching, validating, and preprocessing forex data.
Supports multiple data sources and timeframes with comprehensive validation.
"""

import pandas as pd
import numpy as np
import yfinance as yf
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import backtrader as bt
from config import get_config


class DataHandler:
    """Handles data fetching, validation, and preprocessing for forex backtesting."""
    
    def __init__(self):
        """Initialize the data handler."""
        self.config = get_config()
        self.logger = logging.getLogger(self.__class__.__name__)
        self.data_cache = {}  # Cache for downloaded data
        
        # Forex symbol mapping for yfinance
        self.forex_symbols = {
            'EURUSD': 'EURUSD=X',
            'GBPUSD': 'GBPUSD=X',
            'USDJPY': 'USDJPY=X',
            'AUDUSD': 'AUDUSD=X',
            'USDCAD': 'USDCAD=X',
            'USDCHF': 'USDCHF=X',
            'NZDUSD': 'NZDUSD=X',
            'EURGBP': 'EURGBP=X',
            'EURJPY': 'EURJPY=X',
            'GBPJPY': 'GBPJPY=X',
            'AUDNZD': 'AUDNZD=X',
            'AUDCAD': 'AUDCAD=X',
            'AUDCHF': 'AUDCHF=X',
            'AUDJPY': 'AUDJPY=X',
            'CADJPY': 'CADJPY=X',
            'CHFJPY': 'CHFJPY=X',
            'EURCHF': 'EURCHF=X',
            'EURCAD': 'EURCAD=X',
            'GBPCAD': 'GBPCAD=X',
            'GBPCHF': 'GBPCHF=X',
            'NZDCAD': 'NZDCAD=X',
            'NZDCHF': 'NZDCHF=X',
            'NZDJPY': 'NZDJPY=X',
        }
        
        # Timeframe mapping
        self.timeframe_mapping = {
            'M1': '1m',
            'M5': '5m',
            'M15': '15m',
            'M30': '30m',
            'H1': '1h',
            'H4': '4h',
            'D1': '1d',
            'W1': '1wk',
            'MN1': '1mo'
        }
    
    def fetch_data(self, symbol: str, start_date: datetime, end_date: datetime, 
                   timeframe: str = 'M5') -> Optional[pd.DataFrame]:
        """
        Fetch forex data for a given symbol and timeframe.
        
        Args:
            symbol: Currency pair symbol (e.g., 'EURUSD')
            start_date: Start date for data
            end_date: End date for data
            timeframe: Timeframe (M1, M5, M15, M30, H1, H4, D1, W1, MN1)
        
        Returns:
            DataFrame with OHLCV data or None if failed
        """
        try:
            # Check cache first
            cache_key = f"{symbol}_{timeframe}_{start_date}_{end_date}"
            if cache_key in self.data_cache:
                self.logger.debug(f"Using cached data for {symbol}")
                return self.data_cache[cache_key].copy()
            
            # Get yfinance symbol
            yf_symbol = self.forex_symbols.get(symbol)
            if not yf_symbol:
                self.logger.error(f"Unsupported symbol: {symbol}")
                return None
            
            # Get yfinance interval
            interval = self.timeframe_mapping.get(timeframe, '5m')
            
            self.logger.info(f"Fetching data for {symbol} ({yf_symbol}) "
                           f"from {start_date} to {end_date} with {timeframe} timeframe")
            
            # Fetch data from yfinance
            ticker = yf.Ticker(yf_symbol)
            data = ticker.history(
                start=start_date,
                end=end_date,
                interval=interval,
                auto_adjust=True,
                prepost=False
            )
            
            if data.empty:
                self.logger.warning(f"No data received for {symbol}")
                return None
            
            # Rename columns to standard format
            data = data.rename(columns={
                'Open': 'open',
                'High': 'high',
                'Low': 'low',
                'Close': 'close',
                'Volume': 'volume'
            })
            
            # Ensure we have the required columns
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in data.columns:
                    if col == 'volume':
                        # Forex data might not have volume, create dummy volume
                        data['volume'] = 1000
                    else:
                        self.logger.error(f"Missing required column: {col}")
                        return None
            
            # Clean and validate data
            data = self._clean_data(data, symbol)
            
            if data is None or data.empty:
                self.logger.error(f"Data cleaning failed for {symbol}")
                return None
            
            # Cache the data
            self.data_cache[cache_key] = data.copy()
            
            self.logger.info(f"Successfully fetched {len(data)} bars for {symbol}")
            return data
            
        except Exception as e:
            self.logger.error(f"Error fetching data for {symbol}: {e}")
            return None
    
    def _clean_data(self, data: pd.DataFrame, symbol: str) -> Optional[pd.DataFrame]:
        """
        Clean and validate the fetched data.
        
        Args:
            data: Raw data DataFrame
            symbol: Currency pair symbol
        
        Returns:
            Cleaned DataFrame or None if validation fails
        """
        try:
            # Remove any rows with NaN values
            initial_length = len(data)
            data = data.dropna()
            
            if len(data) < initial_length:
                self.logger.warning(f"Removed {initial_length - len(data)} rows with NaN values")
            
            if data.empty:
                self.logger.error("All data was removed during cleaning")
                return None
            
            # Validate OHLC relationships
            invalid_ohlc = (
                (data['high'] < data['low']) |
                (data['high'] < data['open']) |
                (data['high'] < data['close']) |
                (data['low'] > data['open']) |
                (data['low'] > data['close'])
            )
            
            if invalid_ohlc.any():
                self.logger.warning(f"Found {invalid_ohlc.sum()} bars with invalid OHLC relationships")
                data = data[~invalid_ohlc]
            
            # Remove zero or negative prices
            invalid_prices = (
                (data['open'] <= 0) |
                (data['high'] <= 0) |
                (data['low'] <= 0) |
                (data['close'] <= 0)
            )
            
            if invalid_prices.any():
                self.logger.warning(f"Found {invalid_prices.sum()} bars with invalid prices")
                data = data[~invalid_prices]
            
            # Check for extreme price movements (potential data errors)
            price_changes = data['close'].pct_change().abs()
            extreme_moves = price_changes > 0.1  # 10% move in one bar
            
            if extreme_moves.any():
                self.logger.warning(f"Found {extreme_moves.sum()} bars with extreme price movements")
                # Don't remove these automatically, just log them
            
            # Ensure volume is positive
            if 'volume' in data.columns:
                data.loc[data['volume'] <= 0, 'volume'] = 1000  # Default volume for forex
            
            # Sort by datetime index
            data = data.sort_index()
            
            # Check for duplicate timestamps
            duplicates = data.index.duplicated()
            if duplicates.any():
                self.logger.warning(f"Found {duplicates.sum()} duplicate timestamps")
                data = data[~duplicates]
            
            # Final validation
            if len(data) < 20:  # Minimum data points required (reduced from 100)
                self.logger.error(f"Insufficient data after cleaning: {len(data)} bars")
                return None
            
            self.logger.info(f"Data cleaning completed for {symbol}: {len(data)} valid bars")
            return data
            
        except Exception as e:
            self.logger.error(f"Error cleaning data for {symbol}: {e}")
            return None
    
    def create_backtrader_feed(self, data: pd.DataFrame, name: str = 'data') -> bt.feeds.PandasData:
        """
        Create a Backtrader data feed from pandas DataFrame.
        
        Args:
            data: OHLCV DataFrame
            name: Name for the data feed
        
        Returns:
            Backtrader data feed
        """
        try:
            # Ensure the DataFrame has the correct column order and names
            data_feed = bt.feeds.PandasData(
                dataname=data,
                datetime=None,  # Use index as datetime
                open='open',
                high='high',
                low='low',
                close='close',
                volume='volume',
                openinterest=None,
                name=name
            )
            
            self.logger.debug(f"Created Backtrader feed for {name}")
            return data_feed
            
        except Exception as e:
            self.logger.error(f"Error creating Backtrader feed: {e}")
            return None
    
    def fetch_multiple_pairs(self, pairs: List[str], start_date: datetime, 
                           end_date: datetime, timeframe: str = 'M5') -> Dict[str, pd.DataFrame]:
        """
        Fetch data for multiple currency pairs.
        
        Args:
            pairs: List of currency pair symbols
            start_date: Start date for data
            end_date: End date for data
            timeframe: Timeframe
        
        Returns:
            Dictionary mapping symbols to DataFrames
        """
        data_dict = {}
        
        for pair in pairs:
            self.logger.info(f"Fetching data for {pair}")
            data = self.fetch_data(pair, start_date, end_date, timeframe)
            
            if data is not None:
                data_dict[pair] = data
            else:
                self.logger.warning(f"Failed to fetch data for {pair}")
        
        self.logger.info(f"Successfully fetched data for {len(data_dict)} out of {len(pairs)} pairs")
        return data_dict
    
    def validate_data_quality(self, data: pd.DataFrame, symbol: str) -> Dict[str, Any]:
        """
        Perform comprehensive data quality validation.
        
        Args:
            data: OHLCV DataFrame
            symbol: Currency pair symbol
        
        Returns:
            Dictionary with validation results
        """
        validation_results = {
            'symbol': symbol,
            'total_bars': len(data),
            'date_range': {
                'start': data.index.min(),
                'end': data.index.max()
            },
            'issues': []
        }
        
        try:
            # Check for missing data (gaps)
            time_diff = data.index.to_series().diff()
            expected_interval = time_diff.mode().iloc[0] if not time_diff.mode().empty else None
            
            if expected_interval:
                gaps = time_diff > expected_interval * 2  # Gaps larger than 2x normal interval
                if gaps.any():
                    gap_count = gaps.sum()
                    validation_results['issues'].append(f"Found {gap_count} data gaps")
            
            # Check price consistency
            price_issues = 0
            
            # OHLC relationship validation
            ohlc_issues = (
                (data['high'] < data['low']) |
                (data['high'] < data['open']) |
                (data['high'] < data['close']) |
                (data['low'] > data['open']) |
                (data['low'] > data['close'])
            ).sum()
            
            if ohlc_issues > 0:
                validation_results['issues'].append(f"Found {ohlc_issues} OHLC relationship violations")
                price_issues += ohlc_issues
            
            # Check for extreme price movements
            returns = data['close'].pct_change()
            extreme_returns = (returns.abs() > 0.05).sum()  # 5% moves
            
            if extreme_returns > len(data) * 0.01:  # More than 1% of bars
                validation_results['issues'].append(f"Found {extreme_returns} extreme price movements")
            
            # Check for zero volume (if applicable)
            if 'volume' in data.columns:
                zero_volume = (data['volume'] == 0).sum()
                if zero_volume > 0:
                    validation_results['issues'].append(f"Found {zero_volume} bars with zero volume")
            
            # Statistical validation
            validation_results['statistics'] = {
                'mean_return': returns.mean(),
                'volatility': returns.std(),
                'min_price': data['close'].min(),
                'max_price': data['close'].max(),
                'price_range': data['close'].max() - data['close'].min()
            }
            
            # Overall quality score
            total_issues = sum([
                len([issue for issue in validation_results['issues'] if 'gap' in issue.lower()]),
                price_issues,
                extreme_returns if extreme_returns > len(data) * 0.01 else 0
            ])
            
            quality_score = max(0, 100 - (total_issues / len(data) * 100))
            validation_results['quality_score'] = quality_score
            
            if quality_score >= 95:
                validation_results['quality_rating'] = 'Excellent'
            elif quality_score >= 85:
                validation_results['quality_rating'] = 'Good'
            elif quality_score >= 70:
                validation_results['quality_rating'] = 'Fair'
            else:
                validation_results['quality_rating'] = 'Poor'
            
            self.logger.info(f"Data quality validation for {symbol}: "
                           f"{validation_results['quality_rating']} "
                           f"(Score: {quality_score:.1f})")
            
        except Exception as e:
            validation_results['issues'].append(f"Validation error: {e}")
            validation_results['quality_score'] = 0
            validation_results['quality_rating'] = 'Error'
        
        return validation_results
    
    def get_available_symbols(self) -> List[str]:
        """Get list of available forex symbols."""
        return list(self.forex_symbols.keys())
    
    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """
        Get information about a forex symbol.
        
        Args:
            symbol: Currency pair symbol
        
        Returns:
            Dictionary with symbol information
        """
        if symbol not in self.forex_symbols:
            return {'error': f'Symbol {symbol} not supported'}
        
        return {
            'symbol': symbol,
            'yfinance_symbol': self.forex_symbols[symbol],
            'base_currency': symbol[:3],
            'quote_currency': symbol[3:],
            'supported_timeframes': list(self.timeframe_mapping.keys())
        }
    
    def clear_cache(self):
        """Clear the data cache."""
        self.data_cache.clear()
        self.logger.info("Data cache cleared")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """Get information about cached data."""
        return {
            'cached_datasets': len(self.data_cache),
            'cache_keys': list(self.data_cache.keys()),
            'total_memory_usage': sum(df.memory_usage(deep=True).sum() 
                                    for df in self.data_cache.values())
        }
