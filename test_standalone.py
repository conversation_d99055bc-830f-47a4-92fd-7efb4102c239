"""
Standalone test that bypasses our backtest engine completely.
"""

import backtrader as bt
import pandas as pd
import numpy as np
from datetime import datetime
from data_handler import DataHandler
from strategy.minimal_strategy import MinimalStrategy


def test_standalone():
    """Test with completely standalone setup."""
    print("Testing standalone backtest...")
    
    # Get real data
    data_handler = DataHandler()
    data = data_handler.fetch_data('EURUSD', datetime(2024, 1, 1), datetime(2024, 6, 30), 'D1')
    
    if data is None or data.empty:
        print("Failed to get data")
        return
    
    print(f"Got {len(data)} bars of data")
    print(f"Data columns: {list(data.columns)}")
    print(f"Data index type: {type(data.index)}")
    print(f"First few rows:")
    print(data.head())
    
    # Create Cerebro
    cerebro = bt.Cerebro()
    
    # Add data
    print("Creating data feed...")
    data_feed = bt.feeds.PandasData(dataname=data)
    print(f"Data feed created: {data_feed}")
    
    cerebro.adddata(data_feed)
    print("Data added to Cerebro")
    
    # Add strategy
    print("Adding strategy...")
    cerebro.addstrategy(MinimalStrategy, ema_fast=10, ema_slow=20)
    print("Strategy added")
    
    # Set cash
    cerebro.broker.setcash(10000)
    
    print(f"Starting value: {cerebro.broker.getvalue():.2f}")
    
    # Run
    print("Running backtest...")
    try:
        results = cerebro.run()
        print(f"Final value: {cerebro.broker.getvalue():.2f}")
        print("✅ Standalone test successful!")
        return True
    except Exception as e:
        print(f"❌ Standalone test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    test_standalone()
