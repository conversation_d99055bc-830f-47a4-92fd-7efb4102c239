"""
Test the system components step by step.
"""

import logging
from config import get_config, setup_logging
from data_handler import DataHandler
from strategy import get_strategy
import backtrader as bt

# Setup logging
logging.basicConfig(level=logging.DEBUG)

def test_config():
    """Test configuration loading."""
    print("Testing configuration...")
    try:
        config = get_config()
        print(f"Strategy: {config.get_strategy_name()}")
        print(f"Initial cash: {config.get_initial_cash()}")
        print(f"Start date: {config.get_start_date()}")
        print(f"End date: {config.get_end_date()}")
        print("✅ Configuration test passed")
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_data_handler():
    """Test data handler."""
    print("\nTesting data handler...")
    try:
        config = get_config()
        data_handler = DataHandler()
        
        # Test with a larger date range to get more data
        from datetime import datetime
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 6, 30)
        
        data = data_handler.fetch_data('EURUSD', start_date, end_date, 'D1')
        
        if data is not None and not data.empty:
            print(f"✅ Data handler test passed - fetched {len(data)} bars")
            return data
        else:
            print("❌ Data handler test failed - no data")
            return None
    except Exception as e:
        print(f"❌ Data handler test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_strategy_loading():
    """Test strategy loading."""
    print("\nTesting strategy loading...")
    try:
        from strategy import list_strategies
        strategies = list_strategies()
        print(f"Available strategies: {strategies}")
        
        if 'ema_crossover' in strategies:
            strategy_class = get_strategy('ema_crossover')
            print(f"✅ Strategy loading test passed - got {strategy_class}")
            return strategy_class
        else:
            print("❌ EMA crossover strategy not found")
            return None
    except Exception as e:
        print(f"❌ Strategy loading test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_strategy_with_data(data, strategy_class):
    """Test strategy with actual data."""
    print("\nTesting strategy with data...")
    try:
        # Create Cerebro
        cerebro = bt.Cerebro()
        
        # Add data
        data_feed = bt.feeds.PandasData(dataname=data)
        cerebro.adddata(data_feed)
        
        # Add strategy with minimal parameters (smaller periods for limited data)
        cerebro.addstrategy(
            strategy_class,
            ema_fast=5,
            ema_slow=10,
            trend_confirmation=False  # Disable trend confirmation for test
        )
        
        # Set cash
        cerebro.broker.setcash(10000)
        
        print("Running strategy test...")
        results = cerebro.run()
        
        print(f"✅ Strategy test passed - final value: {cerebro.broker.getvalue():.2f}")
        return True
        
    except Exception as e:
        print(f"❌ Strategy test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🧪 Testing Forex Backtesting System Components")
    print("=" * 50)
    
    # Test configuration
    if not test_config():
        return
    
    # Test data handler
    data = test_data_handler()
    if data is None:
        return
    
    # Test strategy loading
    strategy_class = test_strategy_loading()
    if strategy_class is None:
        return
    
    # Test strategy with data
    if not test_strategy_with_data(data, strategy_class):
        return
    
    print("\n" + "=" * 50)
    print("✅ All component tests passed!")
    print("The system should work correctly now.")

if __name__ == "__main__":
    main()
