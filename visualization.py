"""
Visualization module for creating interactive charts and performance visualizations.
Uses Plotly for interactive charts with candlesticks, indicators, and trade markers.
"""

import plotly.graph_objects as go
import plotly.subplots as sp
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import json

from config import get_config


class ChartVisualizer:
    """Creates interactive charts for backtesting results."""
    
    def __init__(self):
        """Initialize the chart visualizer."""
        self.config = get_config()
        self.logger = logging.getLogger(self.__class__.__name__)
        self.theme = self.config.get_chart_theme()
        
        # Color scheme
        self.colors = {
            'bullish': '#26a69a',
            'bearish': '#ef5350',
            'long_entry': '#4caf50',
            'short_entry': '#f44336',
            'long_exit': '#81c784',
            'short_exit': '#e57373',
            'ema_fast': '#2196f3',
            'ema_slow': '#ff9800',
            'volume': '#9e9e9e',
            'background': '#ffffff' if self.theme == 'plotly_white' else '#1e1e1e'
        }
    
    def create_main_chart(self, data: pd.DataFrame, trades: List[Dict], 
                         indicators: Dict[str, pd.Series], 
                         title: str = "Forex Backtest Results") -> go.Figure:
        """
        Create the main candlestick chart with indicators and trade markers.
        
        Args:
            data: OHLCV data
            trades: List of trade dictionaries
            indicators: Dictionary of indicator series
            title: Chart title
        
        Returns:
            Plotly figure
        """
        try:
            # Create subplots
            fig = make_subplots(
                rows=3, cols=1,
                shared_xaxes=True,
                vertical_spacing=0.05,
                subplot_titles=('Price & Indicators', 'Volume', 'RSI'),
                row_heights=[0.7, 0.15, 0.15]
            )
            
            # Add candlestick chart
            fig.add_trace(
                go.Candlestick(
                    x=data.index,
                    open=data['open'],
                    high=data['high'],
                    low=data['low'],
                    close=data['close'],
                    name='Price',
                    increasing_line_color=self.colors['bullish'],
                    decreasing_line_color=self.colors['bearish']
                ),
                row=1, col=1
            )
            
            # Add indicators
            self._add_indicators_to_chart(fig, data, indicators)
            
            # Add trade markers
            self._add_trade_markers(fig, trades)
            
            # Add volume
            self._add_volume_chart(fig, data)
            
            # Add RSI if available
            if 'rsi' in indicators:
                self._add_rsi_chart(fig, indicators['rsi'])
            
            # Update layout
            fig.update_layout(
                title=title,
                template=self.theme,
                xaxis_rangeslider_visible=False,
                height=800,
                showlegend=True,
                hovermode='x unified'
            )
            
            # Update x-axis
            fig.update_xaxes(
                title_text="Time",
                row=3, col=1
            )
            
            # Update y-axes
            fig.update_yaxes(title_text="Price", row=1, col=1)
            fig.update_yaxes(title_text="Volume", row=2, col=1)
            fig.update_yaxes(title_text="RSI", row=3, col=1, range=[0, 100])
            
            self.logger.info(f"Main chart created with {len(trades)} trades")
            return fig
            
        except Exception as e:
            self.logger.error(f"Error creating main chart: {e}")
            return go.Figure()
    
    def _add_indicators_to_chart(self, fig: go.Figure, data: pd.DataFrame, 
                                indicators: Dict[str, pd.Series]):
        """Add technical indicators to the price chart."""
        try:
            # EMA indicators
            if 'ema_fast' in indicators:
                fig.add_trace(
                    go.Scatter(
                        x=data.index,
                        y=indicators['ema_fast'],
                        mode='lines',
                        name=f'EMA Fast',
                        line=dict(color=self.colors['ema_fast'], width=2)
                    ),
                    row=1, col=1
                )
            
            if 'ema_slow' in indicators:
                fig.add_trace(
                    go.Scatter(
                        x=data.index,
                        y=indicators['ema_slow'],
                        mode='lines',
                        name=f'EMA Slow',
                        line=dict(color=self.colors['ema_slow'], width=2)
                    ),
                    row=1, col=1
                )
            
            # Bollinger Bands
            if all(key in indicators for key in ['bb_upper', 'bb_middle', 'bb_lower']):
                fig.add_trace(
                    go.Scatter(
                        x=data.index,
                        y=indicators['bb_upper'],
                        mode='lines',
                        name='BB Upper',
                        line=dict(color='rgba(128,128,128,0.5)', width=1),
                        showlegend=False
                    ),
                    row=1, col=1
                )
                
                fig.add_trace(
                    go.Scatter(
                        x=data.index,
                        y=indicators['bb_lower'],
                        mode='lines',
                        name='BB Lower',
                        line=dict(color='rgba(128,128,128,0.5)', width=1),
                        fill='tonexty',
                        fillcolor='rgba(128,128,128,0.1)',
                        showlegend=False
                    ),
                    row=1, col=1
                )
                
                fig.add_trace(
                    go.Scatter(
                        x=data.index,
                        y=indicators['bb_middle'],
                        mode='lines',
                        name='BB Middle',
                        line=dict(color='rgba(128,128,128,0.8)', width=1, dash='dash')
                    ),
                    row=1, col=1
                )
            
            # Support and Resistance levels (if available)
            if 'support_levels' in indicators and 'resistance_levels' in indicators:
                # This would need to be implemented based on the breakout strategy
                pass
            
        except Exception as e:
            self.logger.error(f"Error adding indicators to chart: {e}")
    
    def _add_trade_markers(self, fig: go.Figure, trades: List[Dict]):
        """Add trade entry and exit markers to the chart."""
        try:
            long_entries = []
            short_entries = []
            long_exits = []
            short_exits = []
            
            for trade in trades:
                if trade['signal'] == 'LONG':
                    long_entries.append({
                        'x': trade['entry_time'],
                        'y': trade['entry_price'],
                        'text': f"Long Entry<br>Price: {trade['entry_price']:.5f}<br>Size: {trade['size']:.2f}"
                    })
                    if trade.get('exit_price'):
                        long_exits.append({
                            'x': trade['exit_time'],
                            'y': trade['exit_price'],
                            'text': f"Long Exit<br>Price: {trade['exit_price']:.5f}<br>P&L: {trade.get('pnl', 0):.2f}"
                        })
                
                elif trade['signal'] == 'SHORT':
                    short_entries.append({
                        'x': trade['entry_time'],
                        'y': trade['entry_price'],
                        'text': f"Short Entry<br>Price: {trade['entry_price']:.5f}<br>Size: {trade['size']:.2f}"
                    })
                    if trade.get('exit_price'):
                        short_exits.append({
                            'x': trade['exit_time'],
                            'y': trade['exit_price'],
                            'text': f"Short Exit<br>Price: {trade['exit_price']:.5f}<br>P&L: {trade.get('pnl', 0):.2f}"
                        })
            
            # Add long entry markers
            if long_entries:
                fig.add_trace(
                    go.Scatter(
                        x=[entry['x'] for entry in long_entries],
                        y=[entry['y'] for entry in long_entries],
                        mode='markers',
                        name='Long Entry',
                        marker=dict(
                            symbol='triangle-up',
                            size=12,
                            color=self.colors['long_entry']
                        ),
                        text=[entry['text'] for entry in long_entries],
                        hovertemplate='%{text}<extra></extra>'
                    ),
                    row=1, col=1
                )
            
            # Add short entry markers
            if short_entries:
                fig.add_trace(
                    go.Scatter(
                        x=[entry['x'] for entry in short_entries],
                        y=[entry['y'] for entry in short_entries],
                        mode='markers',
                        name='Short Entry',
                        marker=dict(
                            symbol='triangle-down',
                            size=12,
                            color=self.colors['short_entry']
                        ),
                        text=[entry['text'] for entry in short_entries],
                        hovertemplate='%{text}<extra></extra>'
                    ),
                    row=1, col=1
                )
            
            # Add long exit markers
            if long_exits:
                fig.add_trace(
                    go.Scatter(
                        x=[exit['x'] for exit in long_exits],
                        y=[exit['y'] for exit in long_exits],
                        mode='markers',
                        name='Long Exit',
                        marker=dict(
                            symbol='triangle-down',
                            size=10,
                            color=self.colors['long_exit']
                        ),
                        text=[exit['text'] for exit in long_exits],
                        hovertemplate='%{text}<extra></extra>'
                    ),
                    row=1, col=1
                )
            
            # Add short exit markers
            if short_exits:
                fig.add_trace(
                    go.Scatter(
                        x=[exit['x'] for exit in short_exits],
                        y=[exit['y'] for exit in short_exits],
                        mode='markers',
                        name='Short Exit',
                        marker=dict(
                            symbol='triangle-up',
                            size=10,
                            color=self.colors['short_exit']
                        ),
                        text=[exit['text'] for exit in short_exits],
                        hovertemplate='%{text}<extra></extra>'
                    ),
                    row=1, col=1
                )
            
        except Exception as e:
            self.logger.error(f"Error adding trade markers: {e}")
    
    def _add_volume_chart(self, fig: go.Figure, data: pd.DataFrame):
        """Add volume chart to the subplot."""
        try:
            if 'volume' in data.columns:
                colors = [self.colors['bullish'] if close >= open_price else self.colors['bearish']
                         for close, open_price in zip(data['close'], data['open'])]
                
                fig.add_trace(
                    go.Bar(
                        x=data.index,
                        y=data['volume'],
                        name='Volume',
                        marker_color=colors,
                        opacity=0.7
                    ),
                    row=2, col=1
                )
        except Exception as e:
            self.logger.error(f"Error adding volume chart: {e}")
    
    def _add_rsi_chart(self, fig: go.Figure, rsi_data: pd.Series):
        """Add RSI chart to the subplot."""
        try:
            fig.add_trace(
                go.Scatter(
                    x=rsi_data.index,
                    y=rsi_data,
                    mode='lines',
                    name='RSI',
                    line=dict(color='purple', width=2)
                ),
                row=3, col=1
            )
            
            # Add RSI levels
            fig.add_hline(y=70, line_dash="dash", line_color="red", 
                         annotation_text="Overbought", row=3, col=1)
            fig.add_hline(y=30, line_dash="dash", line_color="green", 
                         annotation_text="Oversold", row=3, col=1)
            fig.add_hline(y=50, line_dash="dot", line_color="gray", 
                         annotation_text="Midline", row=3, col=1)
            
        except Exception as e:
            self.logger.error(f"Error adding RSI chart: {e}")
    
    def create_performance_chart(self, results: Dict[str, Any]) -> go.Figure:
        """
        Create performance overview chart.
        
        Args:
            results: Backtest results dictionary
        
        Returns:
            Plotly figure with performance metrics
        """
        try:
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=('Equity Curve', 'Drawdown', 'Monthly Returns', 'Trade Distribution'),
                specs=[[{"secondary_y": False}, {"secondary_y": False}],
                       [{"secondary_y": False}, {"secondary_y": False}]]
            )
            
            # This would need actual equity curve data from the backtest
            # For now, create placeholder charts
            
            # Placeholder equity curve
            dates = pd.date_range(start='2023-01-01', end='2024-01-01', freq='D')
            equity = np.cumsum(np.random.randn(len(dates)) * 0.01) + 1
            
            fig.add_trace(
                go.Scatter(
                    x=dates,
                    y=equity,
                    mode='lines',
                    name='Equity Curve',
                    line=dict(color='blue', width=2)
                ),
                row=1, col=1
            )
            
            # Placeholder drawdown
            drawdown = np.minimum.accumulate(equity) - equity
            fig.add_trace(
                go.Scatter(
                    x=dates,
                    y=drawdown,
                    mode='lines',
                    name='Drawdown',
                    fill='tonexty',
                    line=dict(color='red', width=1)
                ),
                row=1, col=2
            )
            
            fig.update_layout(
                title="Performance Overview",
                template=self.theme,
                height=600,
                showlegend=True
            )
            
            return fig
            
        except Exception as e:
            self.logger.error(f"Error creating performance chart: {e}")
            return go.Figure()
    
    def create_trade_analysis_chart(self, trades: List[Dict]) -> go.Figure:
        """
        Create trade analysis charts.
        
        Args:
            trades: List of trade dictionaries
        
        Returns:
            Plotly figure with trade analysis
        """
        try:
            if not trades:
                return go.Figure()
            
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=('P&L Distribution', 'Trade Duration', 'Win/Loss by Signal', 'Cumulative P&L'),
                specs=[[{"secondary_y": False}, {"secondary_y": False}],
                       [{"secondary_y": False}, {"secondary_y": False}]]
            )
            
            # Extract trade data
            pnls = [trade.get('pnl', 0) for trade in trades]
            signals = [trade['signal'] for trade in trades]
            
            # P&L Distribution
            fig.add_trace(
                go.Histogram(
                    x=pnls,
                    name='P&L Distribution',
                    nbinsx=20,
                    marker_color='lightblue'
                ),
                row=1, col=1
            )
            
            # Win/Loss by Signal
            long_pnls = [pnl for trade, pnl in zip(trades, pnls) if trade['signal'] == 'LONG']
            short_pnls = [pnl for trade, pnl in zip(trades, pnls) if trade['signal'] == 'SHORT']
            
            fig.add_trace(
                go.Box(
                    y=long_pnls,
                    name='Long Trades',
                    marker_color='green'
                ),
                row=2, col=1
            )
            
            fig.add_trace(
                go.Box(
                    y=short_pnls,
                    name='Short Trades',
                    marker_color='red'
                ),
                row=2, col=1
            )
            
            # Cumulative P&L
            cumulative_pnl = np.cumsum(pnls)
            fig.add_trace(
                go.Scatter(
                    x=list(range(len(cumulative_pnl))),
                    y=cumulative_pnl,
                    mode='lines',
                    name='Cumulative P&L',
                    line=dict(color='blue', width=2)
                ),
                row=2, col=2
            )
            
            fig.update_layout(
                title="Trade Analysis",
                template=self.theme,
                height=600,
                showlegend=True
            )
            
            return fig
            
        except Exception as e:
            self.logger.error(f"Error creating trade analysis chart: {e}")
            return go.Figure()
    
    def save_chart(self, fig: go.Figure, filename: str, format: str = 'html'):
        """
        Save chart to file.
        
        Args:
            fig: Plotly figure
            filename: Output filename
            format: Output format ('html', 'png', 'pdf')
        """
        try:
            if format == 'html':
                fig.write_html(filename)
            elif format == 'png':
                fig.write_image(filename, format='png', width=1200, height=800)
            elif format == 'pdf':
                fig.write_image(filename, format='pdf', width=1200, height=800)
            
            self.logger.info(f"Chart saved to {filename}")
            
        except Exception as e:
            self.logger.error(f"Error saving chart: {e}")
    
    def create_strategy_comparison_chart(self, results_list: List[Dict]) -> go.Figure:
        """
        Create comparison chart for multiple strategies.
        
        Args:
            results_list: List of results dictionaries from different strategies
        
        Returns:
            Plotly figure comparing strategies
        """
        try:
            fig = go.Figure()
            
            strategies = []
            returns = []
            sharpe_ratios = []
            max_drawdowns = []
            
            for result in results_list:
                strategy_name = result.get('backtest_info', {}).get('strategy', 'Unknown')
                total_return = result.get('performance_metrics', {}).get('total_return', 0)
                sharpe = result.get('sharpe_ratio', 0)
                max_dd = result.get('drawdown', {}).get('max', {}).get('drawdown', 0)
                
                strategies.append(strategy_name)
                returns.append(total_return * 100)  # Convert to percentage
                sharpe_ratios.append(sharpe)
                max_drawdowns.append(max_dd)
            
            # Create comparison bar chart
            fig.add_trace(go.Bar(
                x=strategies,
                y=returns,
                name='Total Return (%)',
                marker_color='blue'
            ))
            
            fig.update_layout(
                title="Strategy Comparison",
                template=self.theme,
                xaxis_title="Strategy",
                yaxis_title="Total Return (%)",
                height=400
            )
            
            return fig
            
        except Exception as e:
            self.logger.error(f"Error creating strategy comparison chart: {e}")
            return go.Figure()
