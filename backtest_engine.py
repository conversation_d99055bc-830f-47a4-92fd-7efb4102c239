"""
Main backtesting engine that integrates with Backtrader.
Handles strategy execution, portfolio management, and performance tracking.
"""

import backtrader as bt
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
import numpy as np

from config import get_config, setup_logging
from data_handler import DataHandler
from strategy import get_strategy


class BacktestEngine:
    """Main backtesting engine for forex trading strategies."""
    
    def __init__(self):
        """Initialize the backtesting engine."""
        self.config = get_config()
        setup_logging(self.config)
        self.logger = logging.getLogger(self.__class__.__name__)
        
        self.data_handler = DataHandler()
        self.cerebro = None
        self.results = None
        self.strategy_instance = None
        
        self.logger.info("Backtesting engine initialized")
    
    def setup_cerebro(self) -> bt.Cerebro:
        """
        Setup and configure the Backtrader Cerebro engine.
        
        Returns:
            Configured Cerebro instance
        """
        try:
            cerebro = bt.Cerebro()
            
            # Set initial cash
            initial_cash = self.config.get_initial_cash()
            cerebro.broker.setcash(initial_cash)
            
            # Set commission
            commission = self.config.get_commission()
            cerebro.broker.setcommission(commission=commission)
            
            # Add sizers for position sizing
            cerebro.addsizer(bt.sizers.PercentSizer, percents=95)  # Use 95% of available cash
            
            # Add analyzers for performance metrics
            self._add_analyzers(cerebro)
            
            # Add observers for tracking
            cerebro.addobserver(bt.observers.Broker)
            cerebro.addobserver(bt.observers.Trades)
            cerebro.addobserver(bt.observers.BuySell)
            cerebro.addobserver(bt.observers.DrawDown)
            
            self.logger.info(f"Cerebro configured with initial cash: ${initial_cash:,.2f}")
            return cerebro
            
        except Exception as e:
            self.logger.error(f"Error setting up Cerebro: {e}")
            raise
    
    def _add_analyzers(self, cerebro: bt.Cerebro):
        """Add performance analyzers to Cerebro."""
        try:
            # Returns analyzer
            cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')

            # Sharpe ratio analyzer
            cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')

            # Drawdown analyzer
            cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')

            # Trade analyzer
            cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')

            # SQN (System Quality Number) analyzer
            cerebro.addanalyzer(bt.analyzers.SQN, _name='sqn')

            # Try to add optional analyzers
            try:
                cerebro.addanalyzer(bt.analyzers.CalmarRatio, _name='calmar')
            except AttributeError:
                self.logger.warning("CalmarRatio analyzer not available")

            try:
                cerebro.addanalyzer(bt.analyzers.VWR, _name='vwr')
            except AttributeError:
                self.logger.warning("VWR analyzer not available")

            try:
                cerebro.addanalyzer(bt.analyzers.Transactions, _name='transactions')
            except AttributeError:
                self.logger.warning("Transactions analyzer not available")

            try:
                cerebro.addanalyzer(bt.analyzers.PositionsValue, _name='positions')
            except AttributeError:
                self.logger.warning("PositionsValue analyzer not available")

            self.logger.debug("Performance analyzers added to Cerebro")

        except Exception as e:
            self.logger.error(f"Error adding analyzers: {e}")
            raise
    
    def load_data(self, symbol: str) -> Optional[bt.feeds.PandasData]:
        """
        Load data for a specific symbol.
        
        Args:
            symbol: Currency pair symbol
        
        Returns:
            Backtrader data feed or None if failed
        """
        try:
            start_date = self.config.get_start_date()
            end_date = self.config.get_end_date()
            timeframe = self.config.get_timeframe()
            
            self.logger.info(f"Loading data for {symbol} from {start_date} to {end_date}")
            
            # Fetch data
            data = self.data_handler.fetch_data(symbol, start_date, end_date, timeframe)
            
            if data is None or data.empty:
                self.logger.error(f"Failed to load data for {symbol}")
                return None
            
            # Validate data quality
            if self.config.get_validate_data():
                validation_results = self.data_handler.validate_data_quality(data, symbol)
                self.logger.info(f"Data quality for {symbol}: {validation_results['quality_rating']} "
                               f"(Score: {validation_results['quality_score']:.1f})")
                
                if validation_results['quality_score'] < 70:
                    self.logger.warning(f"Poor data quality for {symbol}, consider using different data source")
            
            # Create Backtrader feed
            data_feed = self.data_handler.create_backtrader_feed(data, symbol)
            
            if data_feed is None:
                self.logger.error(f"Failed to create data feed for {symbol}")
                return None
            
            self.logger.info(f"Successfully loaded {len(data)} bars for {symbol}")
            return data_feed
            
        except Exception as e:
            self.logger.error(f"Error loading data for {symbol}: {e}")
            return None
    
    def add_strategy(self, cerebro: bt.Cerebro) -> bool:
        """
        Add the configured strategy to Cerebro.
        
        Args:
            cerebro: Cerebro instance
        
        Returns:
            True if successful, False otherwise
        """
        try:
            strategy_name = self.config.get_strategy_name()
            self.logger.info(f"Adding strategy: {strategy_name}")
            
            # Get strategy class
            strategy_class = get_strategy(strategy_name)
            
            # Prepare strategy parameters from config
            strategy_params = self._get_strategy_parameters()
            
            # Add strategy to cerebro
            cerebro.addstrategy(strategy_class, **strategy_params)
            
            self.logger.info(f"Strategy {strategy_name} added successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error adding strategy: {e}")
            return False
    
    def _get_strategy_parameters(self) -> Dict[str, Any]:
        """Get strategy parameters from configuration."""
        params = {}
        
        # Common parameters
        params['atr_period'] = self.config.get_atr_period()
        params['volatility_multiplier'] = self.config.get_volatility_multiplier()
        params['max_positions'] = self.config.get_max_positions()
        params['position_size_percent'] = self.config.get_position_size_percent()
        params['martingale_multiplier'] = self.config.get_martingale_multiplier()
        params['max_martingale_levels'] = self.config.get_max_martingale_levels()
        params['max_drawdown'] = self.config.get_max_drawdown()
        params['volatility_method'] = self.config.get_volatility_method()
        
        # Strategy-specific parameters
        strategy_name = self.config.get_strategy_name()
        
        if strategy_name == 'ema_crossover':
            params['ema_fast'] = self.config.get_ema_fast()
            params['ema_slow'] = self.config.get_ema_slow()
        
        return params
    
    def run_backtest(self, symbol: str = None) -> Optional[Dict[str, Any]]:
        """
        Run the complete backtest.
        
        Args:
            symbol: Optional specific symbol to test, otherwise uses config
        
        Returns:
            Backtest results dictionary or None if failed
        """
        try:
            self.logger.info("Starting backtest execution")
            
            # Setup Cerebro
            self.cerebro = self.setup_cerebro()
            
            # Determine symbols to test
            if symbol:
                symbols = [symbol]
            else:
                symbols = self.config.get_currency_pairs()
            
            # Load data for all symbols
            data_feeds_loaded = 0
            for sym in symbols:
                data_feed = self.load_data(sym)
                if data_feed:
                    self.cerebro.adddata(data_feed, name=sym)
                    data_feeds_loaded += 1
                else:
                    self.logger.warning(f"Skipping {sym} due to data loading failure")
            
            if data_feeds_loaded == 0:
                self.logger.error("No data feeds loaded, cannot run backtest")
                return None
            
            # Add strategy
            if not self.add_strategy(self.cerebro):
                self.logger.error("Failed to add strategy, cannot run backtest")
                return None
            
            # Record starting values
            starting_value = self.cerebro.broker.getvalue()
            starting_cash = self.cerebro.broker.getcash()
            
            self.logger.info(f"Starting backtest with {data_feeds_loaded} symbols")
            self.logger.info(f"Initial portfolio value: ${starting_value:,.2f}")
            
            # Run the backtest
            self.results = self.cerebro.run()
            
            if not self.results:
                self.logger.error("Backtest execution failed")
                return None
            
            # Get strategy instance
            self.strategy_instance = self.results[0]
            
            # Calculate final values
            final_value = self.cerebro.broker.getvalue()
            final_cash = self.cerebro.broker.getcash()
            
            # Compile results
            backtest_results = self._compile_results(
                starting_value, final_value, starting_cash, final_cash, symbols
            )
            
            self.logger.info(f"Backtest completed successfully")
            self.logger.info(f"Final portfolio value: ${final_value:,.2f}")
            self.logger.info(f"Total return: {backtest_results['total_return']:.2%}")
            
            return backtest_results
            
        except Exception as e:
            self.logger.error(f"Error running backtest: {e}")
            return None
    
    def _compile_results(self, starting_value: float, final_value: float,
                        starting_cash: float, final_cash: float,
                        symbols: List[str]) -> Dict[str, Any]:
        """
        Compile comprehensive backtest results.
        
        Args:
            starting_value: Initial portfolio value
            final_value: Final portfolio value
            starting_cash: Initial cash
            final_cash: Final cash
            symbols: List of symbols tested
        
        Returns:
            Dictionary with comprehensive results
        """
        try:
            results = {
                'backtest_info': {
                    'start_date': self.config.get_start_date(),
                    'end_date': self.config.get_end_date(),
                    'strategy': self.config.get_strategy_name(),
                    'symbols': symbols,
                    'timeframe': self.config.get_timeframe(),
                    'initial_cash': starting_value,
                    'final_value': final_value,
                    'final_cash': final_cash
                },
                'performance_metrics': {
                    'total_return': (final_value - starting_value) / starting_value,
                    'total_pnl': final_value - starting_value,
                    'cash_utilization': (starting_cash - final_cash) / starting_cash
                }
            }
            
            # Extract analyzer results
            if self.strategy_instance:
                analyzers = self.strategy_instance.analyzers
                
                # Returns analyzer
                if hasattr(analyzers, 'returns'):
                    returns_analysis = analyzers.returns.get_analysis()
                    results['returns'] = returns_analysis
                
                # Sharpe ratio
                if hasattr(analyzers, 'sharpe'):
                    sharpe_analysis = analyzers.sharpe.get_analysis()
                    results['sharpe_ratio'] = sharpe_analysis.get('sharperatio', 0)
                
                # Drawdown analysis
                if hasattr(analyzers, 'drawdown'):
                    drawdown_analysis = analyzers.drawdown.get_analysis()
                    results['drawdown'] = drawdown_analysis
                
                # Trade analysis
                if hasattr(analyzers, 'trades'):
                    trade_analysis = analyzers.trades.get_analysis()
                    results['trade_analysis'] = trade_analysis
                
                # SQN (System Quality Number)
                if hasattr(analyzers, 'sqn'):
                    sqn_analysis = analyzers.sqn.get_analysis()
                    results['sqn'] = sqn_analysis.get('sqn', 0)
                
                # Calmar ratio
                if hasattr(analyzers, 'calmar'):
                    calmar_analysis = analyzers.calmar.get_analysis()
                    results['calmar_ratio'] = calmar_analysis.get('calmarratio', 0)
                
                # VWR
                if hasattr(analyzers, 'vwr'):
                    vwr_analysis = analyzers.vwr.get_analysis()
                    results['vwr'] = vwr_analysis.get('vwr', 0)
                
                # Strategy-specific statistics
                if hasattr(self.strategy_instance, 'get_strategy_stats'):
                    strategy_stats = self.strategy_instance.get_strategy_stats()
                    results['strategy_stats'] = strategy_stats
            
            # Calculate additional metrics
            results['performance_metrics'].update(self._calculate_additional_metrics(results))
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error compiling results: {e}")
            return {'error': str(e)}
    
    def _calculate_additional_metrics(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate additional performance metrics."""
        additional_metrics = {}
        
        try:
            # Win rate calculation
            if 'trade_analysis' in results:
                trade_stats = results['trade_analysis']
                total_trades = trade_stats.get('total', {}).get('total', 0)
                won_trades = trade_stats.get('won', {}).get('total', 0)
                
                if total_trades > 0:
                    additional_metrics['win_rate'] = won_trades / total_trades
                    additional_metrics['total_trades'] = total_trades
                    additional_metrics['winning_trades'] = won_trades
                    additional_metrics['losing_trades'] = total_trades - won_trades
                
                # Average win/loss
                avg_win = trade_stats.get('won', {}).get('pnl', {}).get('average', 0)
                avg_loss = trade_stats.get('lost', {}).get('pnl', {}).get('average', 0)
                
                additional_metrics['avg_win'] = avg_win
                additional_metrics['avg_loss'] = abs(avg_loss)
                
                if avg_loss != 0:
                    additional_metrics['win_loss_ratio'] = avg_win / abs(avg_loss)
            
            # Risk-adjusted returns
            if 'drawdown' in results:
                max_drawdown = results['drawdown'].get('max', {}).get('drawdown', 0)
                if max_drawdown > 0:
                    total_return = results['performance_metrics']['total_return']
                    additional_metrics['calmar_ratio_manual'] = total_return / (max_drawdown / 100)
            
            # Profit factor
            if 'trade_analysis' in results:
                trade_stats = results['trade_analysis']
                gross_profit = trade_stats.get('won', {}).get('pnl', {}).get('total', 0)
                gross_loss = abs(trade_stats.get('lost', {}).get('pnl', {}).get('total', 0))
                
                if gross_loss > 0:
                    additional_metrics['profit_factor'] = gross_profit / gross_loss
                
                additional_metrics['gross_profit'] = gross_profit
                additional_metrics['gross_loss'] = gross_loss
            
        except Exception as e:
            self.logger.warning(f"Error calculating additional metrics: {e}")
        
        return additional_metrics
    
    def get_trade_list(self) -> Optional[List[Dict[str, Any]]]:
        """
        Get detailed list of all trades.
        
        Returns:
            List of trade dictionaries or None if no trades
        """
        if not self.strategy_instance:
            return None
        
        try:
            if hasattr(self.strategy_instance, 'positions_info'):
                trades = []
                for position_id, position_info in self.strategy_instance.positions_info.items():
                    if position_info.get('status') == 'closed':
                        trade = {
                            'position_id': position_id,
                            'signal': position_info['signal'],
                            'entry_price': position_info['entry_price'],
                            'exit_price': position_info.get('exit_price'),
                            'size': position_info['size'],
                            'entry_time': position_info['timestamp'],
                            'exit_time': position_info.get('exit_timestamp'),
                            'pnl': position_info.get('pnl', 0),
                            'martingale_level': position_info.get('martingale_level', 0)
                        }
                        trades.append(trade)
                
                return trades
            
        except Exception as e:
            self.logger.error(f"Error getting trade list: {e}")
        
        return None
    
    def plot_results(self, save_path: str = None):
        """
        Plot backtest results using Backtrader's plotting functionality.
        
        Args:
            save_path: Optional path to save the plot
        """
        if not self.cerebro:
            self.logger.error("No backtest results to plot")
            return
        
        try:
            import matplotlib
            matplotlib.use('Agg')  # Use non-interactive backend
            
            # Plot the results
            figs = self.cerebro.plot(style='candlestick', barup='green', bardown='red')
            
            if save_path and figs:
                figs[0][0].savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"Plot saved to {save_path}")
            
        except Exception as e:
            self.logger.error(f"Error plotting results: {e}")
    
    def cleanup(self):
        """Clean up resources."""
        if self.data_handler:
            self.data_handler.clear_cache()
        
        self.cerebro = None
        self.results = None
        self.strategy_instance = None
        
        self.logger.info("Backtesting engine cleaned up")
