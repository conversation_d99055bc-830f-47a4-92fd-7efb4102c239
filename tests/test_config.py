"""
Unit tests for configuration management.
"""

import unittest
import tempfile
import os
from datetime import datetime

from config import ConfigManager


class TestConfigManager(unittest.TestCase):
    """Test cases for ConfigManager class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary config file
        self.temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.ini', delete=False)
        self.temp_config.write("""
[STRATEGY]
strategy_name = test_strategy
ema_fast = 21
ema_slow = 50

[BACKTEST]
initial_cash = 5000
start_date = 2023-01-01
end_date = 2023-12-31

[RISK_MANAGEMENT]
max_drawdown = 0.15
position_size_percent = 0.01
""")
        self.temp_config.close()
        
        self.config_manager = ConfigManager(self.temp_config.name)
    
    def tearDown(self):
        """Clean up test fixtures."""
        os.unlink(self.temp_config.name)
    
    def test_get_strategy_name(self):
        """Test getting strategy name."""
        self.assertEqual(self.config_manager.get_strategy_name(), 'test_strategy')
    
    def test_get_ema_parameters(self):
        """Test getting EMA parameters."""
        self.assertEqual(self.config_manager.get_ema_fast(), 21)
        self.assertEqual(self.config_manager.get_ema_slow(), 50)
    
    def test_get_initial_cash(self):
        """Test getting initial cash."""
        self.assertEqual(self.config_manager.get_initial_cash(), 5000.0)
    
    def test_get_dates(self):
        """Test getting start and end dates."""
        start_date = self.config_manager.get_start_date()
        end_date = self.config_manager.get_end_date()
        
        self.assertEqual(start_date, datetime(2023, 1, 1))
        self.assertEqual(end_date, datetime(2023, 12, 31))
    
    def test_get_risk_parameters(self):
        """Test getting risk management parameters."""
        self.assertEqual(self.config_manager.get_max_drawdown(), 0.15)
        self.assertEqual(self.config_manager.get_position_size_percent(), 0.01)
    
    def test_update_config(self):
        """Test updating configuration values."""
        self.config_manager.update_config('STRATEGY', 'ema_fast', '30')
        self.assertEqual(self.config_manager.get_ema_fast(), 30)
    
    def test_validation_positive_cash(self):
        """Test validation of positive initial cash."""
        # This should not raise an exception
        try:
            self.config_manager._validate_config()
        except ValueError:
            self.fail("Validation failed for valid configuration")
    
    def test_get_all_config(self):
        """Test getting all configuration as dictionary."""
        all_config = self.config_manager.get_all_config()
        
        self.assertIn('STRATEGY', all_config)
        self.assertIn('BACKTEST', all_config)
        self.assertIn('RISK_MANAGEMENT', all_config)
        
        self.assertEqual(all_config['STRATEGY']['strategy_name'], 'test_strategy')


if __name__ == '__main__':
    unittest.main()
