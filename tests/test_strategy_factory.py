"""
Unit tests for strategy factory system.
"""

import unittest
from unittest.mock import Mock

from strategy.base_strategy import BaseStrategy
from strategy.strategy_factory import StrategyRegistry, register_strategy, get_strategy


class MockStrategy(BaseStrategy):
    """Mock strategy for testing."""
    
    params = (
        ('test_param', 10),
    )
    
    def setup_strategy_indicators(self):
        pass
    
    def get_entry_signal(self):
        return None


class TestStrategyFactory(unittest.TestCase):
    """Test cases for strategy factory system."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.registry = StrategyRegistry()
    
    def test_register_strategy(self):
        """Test registering a strategy."""
        self.registry.register('mock_strategy', MockStrategy)
        
        # Check if strategy is registered
        strategies = self.registry.list_strategies()
        self.assertIn('mock_strategy', strategies)
    
    def test_get_strategy(self):
        """Test getting a registered strategy."""
        self.registry.register('mock_strategy', MockStrategy)
        
        strategy_class = self.registry.get_strategy('mock_strategy')
        self.assertEqual(strategy_class, MockStrategy)
    
    def test_get_unknown_strategy(self):
        """Test getting an unknown strategy raises ValueError."""
        with self.assertRaises(ValueError):
            self.registry.get_strategy('unknown_strategy')
    
    def test_get_strategy_info(self):
        """Test getting strategy information."""
        self.registry.register('mock_strategy', MockStrategy)
        
        info = self.registry.get_strategy_info('mock_strategy')
        
        self.assertEqual(info['name'], 'mock_strategy')
        self.assertEqual(info['class_name'], 'MockStrategy')
        self.assertIn('test_param', [param[0] for param in info['parameters']])
    
    def test_list_strategies(self):
        """Test listing all strategies."""
        self.registry.register('strategy1', MockStrategy)
        self.registry.register('strategy2', MockStrategy)
        
        strategies = self.registry.list_strategies()
        
        self.assertIn('strategy1', strategies)
        self.assertIn('strategy2', strategies)
        self.assertEqual(len(strategies), 2)
    
    def test_create_strategy(self):
        """Test creating strategy instance."""
        self.registry.register('mock_strategy', MockStrategy)
        
        strategy_class = self.registry.create_strategy('mock_strategy')
        
        # Should return the strategy class (not instance in this implementation)
        self.assertEqual(strategy_class, MockStrategy)
    
    def test_register_non_strategy_class(self):
        """Test registering non-strategy class raises ValueError."""
        class NotAStrategy:
            pass
        
        with self.assertRaises(ValueError):
            self.registry.register('not_strategy', NotAStrategy)
    
    def test_global_register_function(self):
        """Test global register_strategy function."""
        register_strategy('global_mock', MockStrategy)
        
        # Should be able to get it from global registry
        strategy_class = get_strategy('global_mock')
        self.assertEqual(strategy_class, MockStrategy)


if __name__ == '__main__':
    unittest.main()
