# Forex Backtesting System

A comprehensive, modular Python-based Forex backtesting system with portfolio management capabilities, interactive visualizations, and professional-grade reporting.

## 🚀 Features

- **Modular Strategy Architecture**: Easy-to-extend base strategy class with factory pattern
- **Multiple Built-in Strategies**: EMA Crossover, RSI+MACD, Bollinger Bands, Breakout strategies
- **Advanced Position Management**: Martingale scaling with volatility-based triggers
- **Interactive Visualizations**: Plotly-based charts with candlesticks, indicators, and trade markers
- **Comprehensive Reporting**: HTML reports with performance metrics and risk analysis
- **Flexible Configuration**: External configuration management with easy parameter tuning
- **Professional Risk Management**: Drawdown protection, position sizing, and volatility-based exits
- **Multiple Data Sources**: Support for various forex data providers
- **Export Capabilities**: CSV export for further analysis

## 📋 Requirements

- Python 3.13+
- Dependencies listed in `pyproject.toml`

## 🛠️ Installation

1. Clone or download the project
2. Install dependencies using uv (recommended) or pip:

```bash
# Using uv (recommended)
uv sync

# Or using pip
pip install -r requirements.txt
```

## 🎯 Quick Start

### Basic Usage

Run with default configuration:
```bash
python main.py
```

### Command Line Options

```bash
# Run specific strategy
python main.py --strategy ema_crossover

# Test single currency pair
python main.py --symbol EURUSD

# Custom date range
python main.py --start-date 2023-01-01 --end-date 2023-12-31

# List available strategies
python main.py --list-strategies

# Verbose output
python main.py --verbose

# Skip HTML report generation
python main.py --no-report
```

## 📊 Available Strategies

### 1. EMA Crossover Strategy (`ema_crossover`)
- **Signals**: EMA 55/89 crossover
- **Features**: Trend confirmation, volatility filtering
- **Parameters**: Fast/slow EMA periods, trend confirmation

### 2. RSI + MACD Strategy (`rsi_macd`)
- **Signals**: RSI oversold/overbought + MACD crossover
- **Features**: Dual momentum confirmation
- **Parameters**: RSI levels, MACD periods

### 3. Bollinger Bands Strategy (`bollinger_bands`)
- **Signals**: Mean reversion at band extremes
- **Features**: Volatility-based entries, RSI filtering
- **Parameters**: Band periods, deviation factor

### 4. Breakout Strategy (`breakout`)
- **Signals**: Support/resistance breakouts
- **Features**: Volume confirmation, dynamic level detection
- **Parameters**: Lookback period, volume multiplier

## ⚙️ Configuration

Edit `config.ini` to customize:

```ini
[STRATEGY]
strategy_name = ema_crossover
ema_fast = 55
ema_slow = 89
volatility_method = ATR

[BACKTEST]
initial_cash = 10000
start_date = 2023-01-01
end_date = 2024-01-01

[RISK_MANAGEMENT]
max_drawdown = 0.20
position_size_percent = 0.02
martingale_multiplier = 2.0
```

## 🏗️ Architecture

### Core Components

```
forex-backtesting-system/
├── main.py                 # Main execution script
├── config.py              # Configuration management
├── config.ini             # Configuration file
├── backtest_engine.py     # Backtrader integration
├── data_handler.py        # Data fetching and validation
├── visualization.py       # Interactive charts
├── report_generator.py    # HTML report generation
├── strategy/              # Strategy modules
│   ├── __init__.py
│   ├── base_strategy.py   # Base strategy class
│   ├── strategy_factory.py # Strategy factory pattern
│   ├── ema_crossover_strategy.py
│   ├── rsi_macd_strategy.py
│   ├── bollinger_bands_strategy.py
│   └── breakout_strategy.py
└── templates/             # HTML templates
    └── report_template.html
```

### Strategy Development

Create new strategies by inheriting from `BaseStrategy`:

```python
from strategy.base_strategy import BaseStrategy
from strategy.strategy_factory import register_strategy

@register_strategy('my_strategy')
class MyStrategy(BaseStrategy):
    params = (
        ('my_param', 10),
    )

    def setup_strategy_indicators(self):
        self.my_indicator = bt.indicators.SMA(period=self.params.my_param)

    def get_entry_signal(self):
        if self.my_indicator[0] > self.data.close[0]:
            return 'LONG'
        elif self.my_indicator[0] < self.data.close[0]:
            return 'SHORT'
        return None
```

## 📈 Position Management

### Martingale Scaling
- Automatically scales positions when moving against trend
- Volatility-based trigger distances
- Configurable maximum levels and multipliers
- Risk management with maximum drawdown protection

### Take Profit Strategy
- Dynamic targets based on ATR, Bollinger Bands, or standard deviation
- No traditional stop losses - positions held until profitable
- Volatility-adaptive exit levels

## 📊 Reporting

### HTML Reports Include:
- Executive summary with key metrics
- Interactive price charts with indicators
- Trade analysis and distribution
- Risk metrics and drawdown analysis
- Strategy configuration details

### Key Metrics:
- Total return and P&L
- Sharpe ratio and Calmar ratio
- Maximum drawdown
- Win rate and profit factor
- Trade statistics

## 🔧 Advanced Usage

### Parameter Optimization
```python
# Future feature - parameter grid search
python main.py --optimize --param-range ema_fast:20-100:10
```

### Multiple Strategy Comparison
```python
from report_generator import ReportGenerator

# Compare multiple strategies
results = []
for strategy in ['ema_crossover', 'rsi_macd', 'bollinger_bands']:
    # Run backtest for each strategy
    result = run_strategy(strategy)
    results.append(result)

# Generate comparison report
report_gen = ReportGenerator()
report_gen.generate_summary_report(results)
```

## 🧪 Testing

Run the test suite:
```bash
python -m pytest tests/
```

## 📝 Logging

Logs are written to `backtest.log` and console. Configure logging level in `config.ini`:

```ini
[LOGGING]
log_level = INFO
log_file = backtest.log
console_output = true
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add your strategy or enhancement
4. Include tests and documentation
5. Submit a pull request

### Adding New Strategies

1. Create strategy file in `strategy/` directory
2. Inherit from `BaseStrategy`
3. Implement required methods
4. Register with `@register_strategy` decorator
5. Add configuration parameters to `config.ini`

## 📄 License

This project is licensed under the MIT License.

## ⚠️ Disclaimer

This software is for educational and research purposes only. Past performance does not guarantee future results. Always test strategies thoroughly before using with real money.

## 🆘 Support

For issues and questions:
1. Check the documentation
2. Review existing issues
3. Create a new issue with detailed description

## 🔮 Roadmap

- [ ] Parameter optimization framework
- [ ] Real-time trading integration
- [ ] Additional data sources
- [ ] Machine learning strategy templates
- [ ] Portfolio optimization
- [ ] Risk parity strategies
- [ ] Multi-timeframe analysis
- [ ] Walk-forward optimization