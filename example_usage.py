"""
Example usage script demonstrating the Forex backtesting system.
Shows how to run backtests programmatically and generate reports.
"""

import logging
from datetime import datetime

from config import get_config, setup_logging
from backtest_engine import BacktestEngine
from report_generator import ReportGenerator
from strategy import list_strategies, print_available_strategies


def run_single_strategy_example():
    """Example of running a single strategy backtest."""
    print("="*60)
    print("SINGLE STRATEGY BACKTEST EXAMPLE")
    print("="*60)
    
    try:
        # Initialize configuration and logging
        config = get_config()
        setup_logging(config)
        logger = logging.getLogger(__name__)
        
        logger.info("Starting single strategy example")
        
        # Initialize backtest engine
        engine = BacktestEngine()
        
        # Run backtest with default configuration
        results = engine.run_backtest()
        
        if results:
            print(f"\n✅ Backtest completed successfully!")
            
            # Print key metrics
            performance = results.get('performance_metrics', {})
            print(f"Total Return: {performance.get('total_return', 0):.2%}")
            print(f"Total P&L: ${performance.get('total_pnl', 0):,.2f}")
            print(f"Total Trades: {performance.get('total_trades', 0)}")
            print(f"Win Rate: {performance.get('win_rate', 0):.1%}")
            
            # Generate report
            trades = engine.get_trade_list()
            report_gen = ReportGenerator()
            
            report_path = report_gen.generate_report(
                results=results,
                trades=trades
            )
            
            print(f"📊 Report generated: {report_path}")
            
        else:
            print("❌ Backtest failed")
            
    except Exception as e:
        print(f"❌ Error in single strategy example: {e}")


def run_multiple_strategies_example():
    """Example of comparing multiple strategies."""
    print("\n" + "="*60)
    print("MULTIPLE STRATEGIES COMPARISON EXAMPLE")
    print("="*60)
    
    try:
        # Get available strategies
        available_strategies = list_strategies()
        print(f"Available strategies: {', '.join(available_strategies)}")
        
        # Test strategies to compare (limit to first 3 for demo)
        test_strategies = available_strategies[:3] if len(available_strategies) >= 3 else available_strategies
        
        results_list = []
        
        for strategy_name in test_strategies:
            print(f"\n🔄 Testing strategy: {strategy_name}")
            
            try:
                # Update configuration for this strategy
                config = get_config()
                config.update_config('STRATEGY', 'strategy_name', strategy_name)
                
                # Run backtest
                engine = BacktestEngine()
                results = engine.run_backtest()
                
                if results:
                    results_list.append(results)
                    performance = results.get('performance_metrics', {})
                    print(f"   ✅ {strategy_name}: {performance.get('total_return', 0):.2%} return")
                else:
                    print(f"   ❌ {strategy_name}: Failed")
                    
            except Exception as e:
                print(f"   ❌ {strategy_name}: Error - {e}")
        
        # Generate comparison report
        if len(results_list) > 1:
            report_gen = ReportGenerator()
            comparison_path = report_gen.generate_summary_report(
                results_list, 
                "strategy_comparison_example.html"
            )
            print(f"\n📊 Comparison report generated: {comparison_path}")
        else:
            print("\n⚠️  Not enough successful results for comparison")
            
    except Exception as e:
        print(f"❌ Error in multiple strategies example: {e}")


def demonstrate_configuration_options():
    """Demonstrate different configuration options."""
    print("\n" + "="*60)
    print("CONFIGURATION OPTIONS EXAMPLE")
    print("="*60)
    
    try:
        config = get_config()
        
        print("Current Configuration:")
        print(f"  Strategy: {config.get_strategy_name()}")
        print(f"  Initial Cash: ${config.get_initial_cash():,.2f}")
        print(f"  Period: {config.get_start_date()} to {config.get_end_date()}")
        print(f"  Currency Pairs: {', '.join(config.get_currency_pairs())}")
        print(f"  Position Size: {config.get_position_size_percent():.1%}")
        print(f"  Max Drawdown: {config.get_max_drawdown():.1%}")
        print(f"  Volatility Method: {config.get_volatility_method()}")
        
        # Demonstrate updating configuration
        print("\n🔧 Updating configuration...")
        original_cash = config.get_initial_cash()
        
        config.update_config('BACKTEST', 'initial_cash', '20000')
        print(f"  Initial cash updated: ${original_cash:,.2f} → ${config.get_initial_cash():,.2f}")
        
        # Restore original value
        config.update_config('BACKTEST', 'initial_cash', str(original_cash))
        print(f"  Initial cash restored: ${config.get_initial_cash():,.2f}")
        
    except Exception as e:
        print(f"❌ Error in configuration example: {e}")


def demonstrate_data_handling():
    """Demonstrate data handling capabilities."""
    print("\n" + "="*60)
    print("DATA HANDLING EXAMPLE")
    print("="*60)
    
    try:
        from data_handler import DataHandler
        
        data_handler = DataHandler()
        
        # Show available symbols
        symbols = data_handler.get_available_symbols()
        print(f"Available symbols: {len(symbols)} pairs")
        print(f"Sample symbols: {', '.join(symbols[:10])}")
        
        # Get symbol information
        symbol = 'EURUSD'
        symbol_info = data_handler.get_symbol_info(symbol)
        print(f"\nSymbol info for {symbol}:")
        for key, value in symbol_info.items():
            print(f"  {key}: {value}")
        
        # Demonstrate data fetching (with small date range for demo)
        print(f"\n📊 Fetching sample data for {symbol}...")
        
        config = get_config()
        start_date = config.get_start_date()
        end_date = config.get_end_date()
        
        # Limit to smaller range for demo
        demo_end = datetime(start_date.year, start_date.month + 1, start_date.day)
        
        data = data_handler.fetch_data(symbol, start_date, demo_end, 'M5')
        
        if data is not None:
            print(f"  ✅ Fetched {len(data)} bars")
            print(f"  Date range: {data.index.min()} to {data.index.max()}")
            print(f"  Columns: {list(data.columns)}")
            
            # Validate data quality
            validation = data_handler.validate_data_quality(data, symbol)
            print(f"  Data quality: {validation['quality_rating']} (Score: {validation['quality_score']:.1f})")
            
            if validation['issues']:
                print(f"  Issues found: {', '.join(validation['issues'])}")
        else:
            print("  ❌ Failed to fetch data")
            
    except Exception as e:
        print(f"❌ Error in data handling example: {e}")


def main():
    """Main function to run all examples."""
    print("🚀 Forex Backtesting System - Usage Examples")
    print("=" * 60)
    
    # Show available strategies first
    print("\n📋 Available Strategies:")
    print_available_strategies()
    
    # Run examples
    demonstrate_configuration_options()
    demonstrate_data_handling()
    run_single_strategy_example()
    
    # Uncomment to run multiple strategies comparison (takes longer)
    # run_multiple_strategies_example()
    
    print("\n" + "="*60)
    print("✅ All examples completed!")
    print("📖 Check the generated reports in the 'reports' directory")
    print("📊 For more advanced usage, see the README.md file")
    print("="*60)


if __name__ == "__main__":
    main()
