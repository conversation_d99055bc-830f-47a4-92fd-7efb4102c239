2025-07-13 16:28:28,877 - __main__ - INFO - Forex Backtesting System started
2025-07-13 16:28:28,877 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 16:28:28,877 - __main__ - INFO - Period: 2023-01-01 00:00:00 to 2023-01-31 00:00:00
2025-07-13 16:28:28,877 - __main__ - INFO - Starting backtest execution
2025-07-13 16:28:28,877 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 16:28:28,877 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 16:28:28,878 - BacktestEngine - ERROR - Error setting up Cerebro: module 'backtrader.analyzers' has no attribute 'CalmarRatio'
2025-07-13 16:28:28,878 - BacktestEngine - ERROR - Error running backtest: module 'backtrader.analyzers' has no attribute 'CalmarRatio'
2025-07-13 16:28:28,878 - __main__ - ERROR - Backtest execution failed
2025-07-13 16:28:28,878 - __main__ - ERROR - Backtest failed
2025-07-13 16:28:57,418 - __main__ - INFO - Forex Backtesting System started
2025-07-13 16:28:57,418 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 16:28:57,418 - __main__ - INFO - Period: 2023-01-01 00:00:00 to 2023-01-31 00:00:00
2025-07-13 16:28:57,418 - __main__ - INFO - Starting backtest execution
2025-07-13 16:28:57,418 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 16:28:57,418 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 16:28:57,418 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 16:28:57,418 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 16:28:57,418 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 16:28:57,418 - BacktestEngine - INFO - Loading data for EURUSD from 2023-01-01 00:00:00 to 2023-01-31 00:00:00
2025-07-13 16:28:57,418 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2023-01-01 00:00:00 to 2023-01-31 00:00:00 with M5 timeframe
2025-07-13 16:28:57,419 - yfinance - DEBUG - Entering history()
2025-07-13 16:28:57,426 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 16:28:57,426 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 16:28:57,458 - yfinance - DEBUG -  Entering history()
2025-07-13 16:28:57,462 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2023-01-01 00:00:00+00:00', 'period2': '2023-01-31 00:00:00+00:00', 'interval': '5m', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 16:28:57,462 - yfinance - DEBUG -   Entering get()
2025-07-13 16:28:57,462 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 16:28:57,462 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 16:28:57,462 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1672531200, 'period2': 1675123200, 'interval': '5m', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 16:28:57,463 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 16:28:57,463 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 16:28:57,463 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 16:28:57,463 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 16:28:57,463 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 16:28:57,463 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 16:28:57,464 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 16:28:57,464 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 16:28:57,464 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 16:28:57,464 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 16:28:57,464 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 16:28:57,464 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 16:28:57,464 - yfinance - DEBUG - reusing cookie
2025-07-13 16:28:57,464 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 16:28:59,251 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 16:28:59,251 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 16:28:59,251 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 16:28:59,251 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 16:28:59,400 - yfinance - DEBUG - response code=422
2025-07-13 16:28:59,401 - yfinance - DEBUG - toggling cookie strategy basic -> csrf
2025-07-13 16:28:59,401 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 16:28:59,401 - yfinance - DEBUG - cookie_mode = 'csrf'
2025-07-13 16:28:59,401 - yfinance - DEBUG -      Entering _get_crumb_csrf()
2025-07-13 16:28:59,401 - yfinance - DEBUG -       Entering _get_cookie_csrf()
2025-07-13 16:28:59,401 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 16:28:59,402 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 16:28:59,402 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 16:28:59,402 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 16:28:59,402 - yfinance - DEBUG -       Exiting _get_cookie_csrf()
2025-07-13 16:28:59,645 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 16:28:59,645 - yfinance - DEBUG -      Exiting _get_crumb_csrf()
2025-07-13 16:28:59,646 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 16:28:59,697 - yfinance - DEBUG - response code=422
2025-07-13 16:28:59,697 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 16:28:59,697 - yfinance - DEBUG -   Exiting get()
2025-07-13 16:28:59,699 - yfinance - ERROR - $EURUSD=X: possibly delisted; no price data found  (5m 2023-01-01 00:00:00 -> 2023-01-31 00:00:00) (Yahoo error = "5m data not available for startTime=1672531200 and endTime=1675123200. The requested range must be within the last 60 days.")
2025-07-13 16:28:59,699 - yfinance - DEBUG -  Exiting history()
2025-07-13 16:28:59,700 - yfinance - DEBUG - Exiting history()
2025-07-13 16:28:59,700 - DataHandler - WARNING - No data received for EURUSD
2025-07-13 16:28:59,700 - BacktestEngine - ERROR - Failed to load data for EURUSD
2025-07-13 16:28:59,700 - BacktestEngine - WARNING - Skipping EURUSD due to data loading failure
2025-07-13 16:28:59,700 - BacktestEngine - ERROR - No data feeds loaded, cannot run backtest
2025-07-13 16:28:59,700 - __main__ - ERROR - Backtest execution failed
2025-07-13 16:28:59,700 - __main__ - ERROR - Backtest failed
2025-07-13 16:29:10,886 - __main__ - INFO - Forex Backtesting System started
2025-07-13 16:29:10,889 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 16:29:10,889 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-01-31 00:00:00
2025-07-13 16:29:10,889 - __main__ - INFO - Starting backtest execution
2025-07-13 16:29:10,889 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 16:29:10,889 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 16:29:10,889 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 16:29:10,889 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 16:29:10,889 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 16:29:10,889 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-01-31 00:00:00
2025-07-13 16:29:10,889 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-01-31 00:00:00 with M5 timeframe
2025-07-13 16:29:10,890 - yfinance - DEBUG - Entering history()
2025-07-13 16:29:10,898 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 16:29:10,899 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 16:29:10,914 - yfinance - DEBUG -  Entering history()
2025-07-13 16:29:10,916 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-01-31 00:00:00+00:00', 'interval': '5m', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 16:29:10,916 - yfinance - DEBUG -   Entering get()
2025-07-13 16:29:10,916 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 16:29:10,916 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 16:29:10,916 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1706659200, 'interval': '5m', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 16:29:10,916 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 16:29:10,916 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 16:29:10,916 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 16:29:10,916 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 16:29:10,916 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 16:29:10,917 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 16:29:10,917 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 16:29:10,918 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 16:29:10,918 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 16:29:10,918 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 16:29:10,918 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 16:29:10,918 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 16:29:10,918 - yfinance - DEBUG - reusing cookie
2025-07-13 16:29:10,918 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 16:29:11,246 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 16:29:11,247 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 16:29:11,247 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 16:29:11,247 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 16:29:11,400 - yfinance - DEBUG - response code=422
2025-07-13 16:29:11,401 - yfinance - DEBUG - toggling cookie strategy basic -> csrf
2025-07-13 16:29:11,402 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 16:29:11,402 - yfinance - DEBUG - cookie_mode = 'csrf'
2025-07-13 16:29:11,402 - yfinance - DEBUG -      Entering _get_crumb_csrf()
2025-07-13 16:29:11,402 - yfinance - DEBUG -       Entering _get_cookie_csrf()
2025-07-13 16:29:11,402 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 16:29:11,403 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 16:29:11,403 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 16:29:11,403 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 16:29:11,403 - yfinance - DEBUG -       Exiting _get_cookie_csrf()
2025-07-13 16:29:11,641 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 16:29:11,641 - yfinance - DEBUG -      Exiting _get_crumb_csrf()
2025-07-13 16:29:11,641 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 16:29:11,694 - yfinance - DEBUG - response code=422
2025-07-13 16:29:11,695 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 16:29:11,695 - yfinance - DEBUG -   Exiting get()
2025-07-13 16:29:11,699 - yfinance - ERROR - $EURUSD=X: possibly delisted; no price data found  (5m 2024-01-01 00:00:00 -> 2024-01-31 00:00:00) (Yahoo error = "5m data not available for startTime=1704067200 and endTime=1706659200. The requested range must be within the last 60 days.")
2025-07-13 16:29:11,702 - yfinance - DEBUG -  Exiting history()
2025-07-13 16:29:11,702 - yfinance - DEBUG - Exiting history()
2025-07-13 16:29:11,702 - DataHandler - WARNING - No data received for EURUSD
2025-07-13 16:29:11,702 - BacktestEngine - ERROR - Failed to load data for EURUSD
2025-07-13 16:29:11,702 - BacktestEngine - WARNING - Skipping EURUSD due to data loading failure
2025-07-13 16:29:11,703 - BacktestEngine - ERROR - No data feeds loaded, cannot run backtest
2025-07-13 16:29:11,703 - __main__ - ERROR - Backtest execution failed
2025-07-13 16:29:11,703 - __main__ - ERROR - Backtest failed
2025-07-13 16:29:45,544 - __main__ - INFO - Forex Backtesting System started
2025-07-13 16:29:45,545 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 16:29:45,545 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 16:29:45,545 - __main__ - INFO - Starting backtest execution
2025-07-13 16:29:45,545 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 16:29:45,545 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 16:29:45,545 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 16:29:45,545 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 16:29:45,545 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 16:29:45,545 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 16:29:45,545 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 16:29:45,545 - yfinance - DEBUG - Entering history()
2025-07-13 16:29:45,549 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 16:29:45,550 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 16:29:45,564 - yfinance - DEBUG -  Entering history()
2025-07-13 16:29:45,567 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 16:29:45,568 - yfinance - DEBUG -   Entering get()
2025-07-13 16:29:45,568 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 16:29:45,568 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 16:29:45,568 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 16:29:45,568 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 16:29:45,568 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 16:29:45,568 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 16:29:45,568 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 16:29:45,568 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 16:29:45,569 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 16:29:45,569 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 16:29:45,569 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 16:29:45,570 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 16:29:45,570 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 16:29:45,570 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 16:29:45,570 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 16:29:45,570 - yfinance - DEBUG - reusing cookie
2025-07-13 16:29:45,570 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 16:29:45,926 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 16:29:45,926 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 16:29:45,926 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 16:29:45,926 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 16:29:46,084 - yfinance - DEBUG - response code=200
2025-07-13 16:29:46,084 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 16:29:46,084 - yfinance - DEBUG -   Exiting get()
2025-07-13 16:29:46,094 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 16:29:46,098 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 16:29:46,108 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 16:29:46,114 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 16:29:46,117 - yfinance - DEBUG -  Exiting history()
2025-07-13 16:29:46,117 - yfinance - DEBUG - Exiting history()
2025-07-13 16:29:46,117 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 16:29:46,119 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 16:29:46,120 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 16:29:46,123 - DataHandler - INFO - Data quality validation for EURUSD: Excellent (Score: 99.6)
2025-07-13 16:29:46,125 - BacktestEngine - INFO - Data quality for EURUSD: Excellent (Score: 99.6)
2025-07-13 16:29:46,126 - DataHandler - DEBUG - Created Backtrader feed for EURUSD
2025-07-13 16:29:46,126 - BacktestEngine - INFO - Successfully loaded 254 bars for EURUSD
2025-07-13 16:29:46,126 - BacktestEngine - ERROR - Error running backtest: 'NoneType' object has no attribute 'addindicator'
2025-07-13 16:29:46,126 - __main__ - ERROR - Backtest execution failed
2025-07-13 16:29:46,126 - __main__ - ERROR - Backtest failed
2025-07-13 16:30:34,116 - __main__ - INFO - Forex Backtesting System started
2025-07-13 16:30:34,116 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 16:30:34,117 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 16:30:34,117 - __main__ - INFO - Starting backtest execution
2025-07-13 16:30:34,117 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 16:30:34,117 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 16:30:34,117 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 16:30:34,117 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 16:30:34,117 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 16:30:34,117 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 16:30:34,117 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 16:30:34,117 - yfinance - DEBUG - Entering history()
2025-07-13 16:30:34,123 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 16:30:34,123 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 16:30:34,138 - yfinance - DEBUG -  Entering history()
2025-07-13 16:30:34,142 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 16:30:34,143 - yfinance - DEBUG -   Entering get()
2025-07-13 16:30:34,143 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 16:30:34,143 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 16:30:34,143 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 16:30:34,143 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 16:30:34,143 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 16:30:34,143 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 16:30:34,143 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 16:30:34,143 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 16:30:34,144 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 16:30:34,145 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 16:30:34,145 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 16:30:34,145 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 16:30:34,145 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 16:30:34,145 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 16:30:34,145 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 16:30:34,145 - yfinance - DEBUG - reusing cookie
2025-07-13 16:30:34,145 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 16:30:34,483 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 16:30:34,484 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 16:30:34,484 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 16:30:34,484 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 16:30:34,625 - yfinance - DEBUG - response code=200
2025-07-13 16:30:34,625 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 16:30:34,625 - yfinance - DEBUG -   Exiting get()
2025-07-13 16:30:34,636 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 16:30:34,640 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 16:30:34,646 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 16:30:34,650 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 16:30:34,650 - yfinance - DEBUG -  Exiting history()
2025-07-13 16:30:34,650 - yfinance - DEBUG - Exiting history()
2025-07-13 16:30:34,652 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 16:30:34,654 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 16:30:34,654 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 16:30:34,657 - DataHandler - INFO - Data quality validation for EURUSD: Excellent (Score: 99.6)
2025-07-13 16:30:34,657 - BacktestEngine - INFO - Data quality for EURUSD: Excellent (Score: 99.6)
2025-07-13 16:30:34,657 - DataHandler - DEBUG - Created Backtrader feed for EURUSD
2025-07-13 16:30:34,657 - BacktestEngine - INFO - Successfully loaded 254 bars for EURUSD
2025-07-13 16:30:34,657 - BacktestEngine - ERROR - Error running backtest: 'NoneType' object has no attribute 'addindicator'
2025-07-13 16:30:34,657 - __main__ - ERROR - Backtest execution failed
2025-07-13 16:30:34,658 - __main__ - ERROR - Backtest failed
2025-07-13 16:31:56,383 - __main__ - INFO - Forex Backtesting System started
2025-07-13 16:31:56,383 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 16:31:56,383 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 16:31:56,383 - __main__ - INFO - Starting backtest execution
2025-07-13 16:31:56,383 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 16:31:56,383 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 16:31:56,383 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 16:31:56,384 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 16:31:56,384 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 16:31:56,384 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 16:31:56,384 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 16:31:56,384 - yfinance - DEBUG - Entering history()
2025-07-13 16:31:56,390 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 16:31:56,390 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 16:31:56,407 - yfinance - DEBUG -  Entering history()
2025-07-13 16:31:56,410 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 16:31:56,411 - yfinance - DEBUG -   Entering get()
2025-07-13 16:31:56,411 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 16:31:56,411 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 16:31:56,411 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 16:31:56,411 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 16:31:56,411 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 16:31:56,411 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 16:31:56,411 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 16:31:56,411 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 16:31:56,412 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 16:31:56,413 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 16:31:56,413 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 16:31:56,413 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 16:31:56,413 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 16:31:56,413 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 16:31:56,413 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 16:31:56,413 - yfinance - DEBUG - reusing cookie
2025-07-13 16:31:56,414 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 16:31:56,746 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 16:31:56,746 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 16:31:56,746 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 16:31:56,747 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 16:31:56,888 - yfinance - DEBUG - response code=200
2025-07-13 16:31:56,889 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 16:31:56,889 - yfinance - DEBUG -   Exiting get()
2025-07-13 16:31:56,899 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 16:31:56,903 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 16:31:56,908 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 16:31:56,913 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 16:31:56,913 - yfinance - DEBUG -  Exiting history()
2025-07-13 16:31:56,913 - yfinance - DEBUG - Exiting history()
2025-07-13 16:31:56,913 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 16:31:56,915 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 16:31:56,915 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 16:31:56,919 - DataHandler - INFO - Data quality validation for EURUSD: Excellent (Score: 99.6)
2025-07-13 16:31:56,919 - BacktestEngine - INFO - Data quality for EURUSD: Excellent (Score: 99.6)
2025-07-13 16:31:56,919 - DataHandler - DEBUG - Created Backtrader feed for EURUSD
2025-07-13 16:31:56,919 - BacktestEngine - INFO - Successfully loaded 254 bars for EURUSD
2025-07-13 16:31:56,919 - BacktestEngine - ERROR - Error running backtest: 'NoneType' object has no attribute 'addindicator'
2025-07-13 16:31:56,919 - __main__ - ERROR - Backtest execution failed
2025-07-13 16:31:56,919 - __main__ - ERROR - Backtest failed
