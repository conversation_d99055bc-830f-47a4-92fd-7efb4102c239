2025-07-13 16:28:28,877 - __main__ - INFO - Forex Backtesting System started
2025-07-13 16:28:28,877 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 16:28:28,877 - __main__ - INFO - Period: 2023-01-01 00:00:00 to 2023-01-31 00:00:00
2025-07-13 16:28:28,877 - __main__ - INFO - Starting backtest execution
2025-07-13 16:28:28,877 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 16:28:28,877 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 16:28:28,878 - BacktestEngine - ERROR - Error setting up Cerebro: module 'backtrader.analyzers' has no attribute 'CalmarRatio'
2025-07-13 16:28:28,878 - BacktestEngine - ERROR - Error running backtest: module 'backtrader.analyzers' has no attribute 'CalmarRatio'
2025-07-13 16:28:28,878 - __main__ - ERROR - Backtest execution failed
2025-07-13 16:28:28,878 - __main__ - ERROR - Backtest failed
2025-07-13 16:28:57,418 - __main__ - INFO - Forex Backtesting System started
2025-07-13 16:28:57,418 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 16:28:57,418 - __main__ - INFO - Period: 2023-01-01 00:00:00 to 2023-01-31 00:00:00
2025-07-13 16:28:57,418 - __main__ - INFO - Starting backtest execution
2025-07-13 16:28:57,418 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 16:28:57,418 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 16:28:57,418 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 16:28:57,418 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 16:28:57,418 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 16:28:57,418 - BacktestEngine - INFO - Loading data for EURUSD from 2023-01-01 00:00:00 to 2023-01-31 00:00:00
2025-07-13 16:28:57,418 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2023-01-01 00:00:00 to 2023-01-31 00:00:00 with M5 timeframe
2025-07-13 16:28:57,419 - yfinance - DEBUG - Entering history()
2025-07-13 16:28:57,426 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 16:28:57,426 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 16:28:57,458 - yfinance - DEBUG -  Entering history()
2025-07-13 16:28:57,462 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2023-01-01 00:00:00+00:00', 'period2': '2023-01-31 00:00:00+00:00', 'interval': '5m', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 16:28:57,462 - yfinance - DEBUG -   Entering get()
2025-07-13 16:28:57,462 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 16:28:57,462 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 16:28:57,462 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1672531200, 'period2': 1675123200, 'interval': '5m', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 16:28:57,463 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 16:28:57,463 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 16:28:57,463 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 16:28:57,463 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 16:28:57,463 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 16:28:57,463 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 16:28:57,464 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 16:28:57,464 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 16:28:57,464 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 16:28:57,464 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 16:28:57,464 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 16:28:57,464 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 16:28:57,464 - yfinance - DEBUG - reusing cookie
2025-07-13 16:28:57,464 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 16:28:59,251 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 16:28:59,251 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 16:28:59,251 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 16:28:59,251 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 16:28:59,400 - yfinance - DEBUG - response code=422
2025-07-13 16:28:59,401 - yfinance - DEBUG - toggling cookie strategy basic -> csrf
2025-07-13 16:28:59,401 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 16:28:59,401 - yfinance - DEBUG - cookie_mode = 'csrf'
2025-07-13 16:28:59,401 - yfinance - DEBUG -      Entering _get_crumb_csrf()
2025-07-13 16:28:59,401 - yfinance - DEBUG -       Entering _get_cookie_csrf()
2025-07-13 16:28:59,401 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 16:28:59,402 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 16:28:59,402 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 16:28:59,402 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 16:28:59,402 - yfinance - DEBUG -       Exiting _get_cookie_csrf()
2025-07-13 16:28:59,645 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 16:28:59,645 - yfinance - DEBUG -      Exiting _get_crumb_csrf()
2025-07-13 16:28:59,646 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 16:28:59,697 - yfinance - DEBUG - response code=422
2025-07-13 16:28:59,697 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 16:28:59,697 - yfinance - DEBUG -   Exiting get()
2025-07-13 16:28:59,699 - yfinance - ERROR - $EURUSD=X: possibly delisted; no price data found  (5m 2023-01-01 00:00:00 -> 2023-01-31 00:00:00) (Yahoo error = "5m data not available for startTime=1672531200 and endTime=1675123200. The requested range must be within the last 60 days.")
2025-07-13 16:28:59,699 - yfinance - DEBUG -  Exiting history()
2025-07-13 16:28:59,700 - yfinance - DEBUG - Exiting history()
2025-07-13 16:28:59,700 - DataHandler - WARNING - No data received for EURUSD
2025-07-13 16:28:59,700 - BacktestEngine - ERROR - Failed to load data for EURUSD
2025-07-13 16:28:59,700 - BacktestEngine - WARNING - Skipping EURUSD due to data loading failure
2025-07-13 16:28:59,700 - BacktestEngine - ERROR - No data feeds loaded, cannot run backtest
2025-07-13 16:28:59,700 - __main__ - ERROR - Backtest execution failed
2025-07-13 16:28:59,700 - __main__ - ERROR - Backtest failed
2025-07-13 16:29:10,886 - __main__ - INFO - Forex Backtesting System started
2025-07-13 16:29:10,889 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 16:29:10,889 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-01-31 00:00:00
2025-07-13 16:29:10,889 - __main__ - INFO - Starting backtest execution
2025-07-13 16:29:10,889 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 16:29:10,889 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 16:29:10,889 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 16:29:10,889 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 16:29:10,889 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 16:29:10,889 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-01-31 00:00:00
2025-07-13 16:29:10,889 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-01-31 00:00:00 with M5 timeframe
2025-07-13 16:29:10,890 - yfinance - DEBUG - Entering history()
2025-07-13 16:29:10,898 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 16:29:10,899 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 16:29:10,914 - yfinance - DEBUG -  Entering history()
2025-07-13 16:29:10,916 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-01-31 00:00:00+00:00', 'interval': '5m', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 16:29:10,916 - yfinance - DEBUG -   Entering get()
2025-07-13 16:29:10,916 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 16:29:10,916 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 16:29:10,916 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1706659200, 'interval': '5m', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 16:29:10,916 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 16:29:10,916 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 16:29:10,916 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 16:29:10,916 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 16:29:10,916 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 16:29:10,917 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 16:29:10,917 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 16:29:10,918 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 16:29:10,918 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 16:29:10,918 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 16:29:10,918 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 16:29:10,918 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 16:29:10,918 - yfinance - DEBUG - reusing cookie
2025-07-13 16:29:10,918 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 16:29:11,246 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 16:29:11,247 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 16:29:11,247 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 16:29:11,247 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 16:29:11,400 - yfinance - DEBUG - response code=422
2025-07-13 16:29:11,401 - yfinance - DEBUG - toggling cookie strategy basic -> csrf
2025-07-13 16:29:11,402 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 16:29:11,402 - yfinance - DEBUG - cookie_mode = 'csrf'
2025-07-13 16:29:11,402 - yfinance - DEBUG -      Entering _get_crumb_csrf()
2025-07-13 16:29:11,402 - yfinance - DEBUG -       Entering _get_cookie_csrf()
2025-07-13 16:29:11,402 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 16:29:11,403 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 16:29:11,403 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 16:29:11,403 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 16:29:11,403 - yfinance - DEBUG -       Exiting _get_cookie_csrf()
2025-07-13 16:29:11,641 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 16:29:11,641 - yfinance - DEBUG -      Exiting _get_crumb_csrf()
2025-07-13 16:29:11,641 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 16:29:11,694 - yfinance - DEBUG - response code=422
2025-07-13 16:29:11,695 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 16:29:11,695 - yfinance - DEBUG -   Exiting get()
2025-07-13 16:29:11,699 - yfinance - ERROR - $EURUSD=X: possibly delisted; no price data found  (5m 2024-01-01 00:00:00 -> 2024-01-31 00:00:00) (Yahoo error = "5m data not available for startTime=1704067200 and endTime=1706659200. The requested range must be within the last 60 days.")
2025-07-13 16:29:11,702 - yfinance - DEBUG -  Exiting history()
2025-07-13 16:29:11,702 - yfinance - DEBUG - Exiting history()
2025-07-13 16:29:11,702 - DataHandler - WARNING - No data received for EURUSD
2025-07-13 16:29:11,702 - BacktestEngine - ERROR - Failed to load data for EURUSD
2025-07-13 16:29:11,702 - BacktestEngine - WARNING - Skipping EURUSD due to data loading failure
2025-07-13 16:29:11,703 - BacktestEngine - ERROR - No data feeds loaded, cannot run backtest
2025-07-13 16:29:11,703 - __main__ - ERROR - Backtest execution failed
2025-07-13 16:29:11,703 - __main__ - ERROR - Backtest failed
2025-07-13 16:29:45,544 - __main__ - INFO - Forex Backtesting System started
2025-07-13 16:29:45,545 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 16:29:45,545 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 16:29:45,545 - __main__ - INFO - Starting backtest execution
2025-07-13 16:29:45,545 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 16:29:45,545 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 16:29:45,545 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 16:29:45,545 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 16:29:45,545 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 16:29:45,545 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 16:29:45,545 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 16:29:45,545 - yfinance - DEBUG - Entering history()
2025-07-13 16:29:45,549 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 16:29:45,550 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 16:29:45,564 - yfinance - DEBUG -  Entering history()
2025-07-13 16:29:45,567 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 16:29:45,568 - yfinance - DEBUG -   Entering get()
2025-07-13 16:29:45,568 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 16:29:45,568 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 16:29:45,568 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 16:29:45,568 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 16:29:45,568 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 16:29:45,568 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 16:29:45,568 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 16:29:45,568 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 16:29:45,569 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 16:29:45,569 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 16:29:45,569 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 16:29:45,570 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 16:29:45,570 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 16:29:45,570 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 16:29:45,570 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 16:29:45,570 - yfinance - DEBUG - reusing cookie
2025-07-13 16:29:45,570 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 16:29:45,926 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 16:29:45,926 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 16:29:45,926 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 16:29:45,926 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 16:29:46,084 - yfinance - DEBUG - response code=200
2025-07-13 16:29:46,084 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 16:29:46,084 - yfinance - DEBUG -   Exiting get()
2025-07-13 16:29:46,094 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 16:29:46,098 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 16:29:46,108 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 16:29:46,114 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 16:29:46,117 - yfinance - DEBUG -  Exiting history()
2025-07-13 16:29:46,117 - yfinance - DEBUG - Exiting history()
2025-07-13 16:29:46,117 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 16:29:46,119 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 16:29:46,120 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 16:29:46,123 - DataHandler - INFO - Data quality validation for EURUSD: Excellent (Score: 99.6)
2025-07-13 16:29:46,125 - BacktestEngine - INFO - Data quality for EURUSD: Excellent (Score: 99.6)
2025-07-13 16:29:46,126 - DataHandler - DEBUG - Created Backtrader feed for EURUSD
2025-07-13 16:29:46,126 - BacktestEngine - INFO - Successfully loaded 254 bars for EURUSD
2025-07-13 16:29:46,126 - BacktestEngine - ERROR - Error running backtest: 'NoneType' object has no attribute 'addindicator'
2025-07-13 16:29:46,126 - __main__ - ERROR - Backtest execution failed
2025-07-13 16:29:46,126 - __main__ - ERROR - Backtest failed
2025-07-13 16:30:34,116 - __main__ - INFO - Forex Backtesting System started
2025-07-13 16:30:34,116 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 16:30:34,117 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 16:30:34,117 - __main__ - INFO - Starting backtest execution
2025-07-13 16:30:34,117 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 16:30:34,117 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 16:30:34,117 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 16:30:34,117 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 16:30:34,117 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 16:30:34,117 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 16:30:34,117 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 16:30:34,117 - yfinance - DEBUG - Entering history()
2025-07-13 16:30:34,123 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 16:30:34,123 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 16:30:34,138 - yfinance - DEBUG -  Entering history()
2025-07-13 16:30:34,142 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 16:30:34,143 - yfinance - DEBUG -   Entering get()
2025-07-13 16:30:34,143 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 16:30:34,143 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 16:30:34,143 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 16:30:34,143 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 16:30:34,143 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 16:30:34,143 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 16:30:34,143 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 16:30:34,143 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 16:30:34,144 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 16:30:34,145 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 16:30:34,145 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 16:30:34,145 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 16:30:34,145 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 16:30:34,145 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 16:30:34,145 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 16:30:34,145 - yfinance - DEBUG - reusing cookie
2025-07-13 16:30:34,145 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 16:30:34,483 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 16:30:34,484 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 16:30:34,484 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 16:30:34,484 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 16:30:34,625 - yfinance - DEBUG - response code=200
2025-07-13 16:30:34,625 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 16:30:34,625 - yfinance - DEBUG -   Exiting get()
2025-07-13 16:30:34,636 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 16:30:34,640 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 16:30:34,646 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 16:30:34,650 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 16:30:34,650 - yfinance - DEBUG -  Exiting history()
2025-07-13 16:30:34,650 - yfinance - DEBUG - Exiting history()
2025-07-13 16:30:34,652 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 16:30:34,654 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 16:30:34,654 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 16:30:34,657 - DataHandler - INFO - Data quality validation for EURUSD: Excellent (Score: 99.6)
2025-07-13 16:30:34,657 - BacktestEngine - INFO - Data quality for EURUSD: Excellent (Score: 99.6)
2025-07-13 16:30:34,657 - DataHandler - DEBUG - Created Backtrader feed for EURUSD
2025-07-13 16:30:34,657 - BacktestEngine - INFO - Successfully loaded 254 bars for EURUSD
2025-07-13 16:30:34,657 - BacktestEngine - ERROR - Error running backtest: 'NoneType' object has no attribute 'addindicator'
2025-07-13 16:30:34,657 - __main__ - ERROR - Backtest execution failed
2025-07-13 16:30:34,658 - __main__ - ERROR - Backtest failed
2025-07-13 16:31:56,383 - __main__ - INFO - Forex Backtesting System started
2025-07-13 16:31:56,383 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 16:31:56,383 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 16:31:56,383 - __main__ - INFO - Starting backtest execution
2025-07-13 16:31:56,383 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 16:31:56,383 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 16:31:56,383 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 16:31:56,384 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 16:31:56,384 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 16:31:56,384 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 16:31:56,384 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 16:31:56,384 - yfinance - DEBUG - Entering history()
2025-07-13 16:31:56,390 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 16:31:56,390 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 16:31:56,407 - yfinance - DEBUG -  Entering history()
2025-07-13 16:31:56,410 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 16:31:56,411 - yfinance - DEBUG -   Entering get()
2025-07-13 16:31:56,411 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 16:31:56,411 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 16:31:56,411 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 16:31:56,411 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 16:31:56,411 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 16:31:56,411 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 16:31:56,411 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 16:31:56,411 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 16:31:56,412 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 16:31:56,413 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 16:31:56,413 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 16:31:56,413 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 16:31:56,413 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 16:31:56,413 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 16:31:56,413 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 16:31:56,413 - yfinance - DEBUG - reusing cookie
2025-07-13 16:31:56,414 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 16:31:56,746 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 16:31:56,746 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 16:31:56,746 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 16:31:56,747 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 16:31:56,888 - yfinance - DEBUG - response code=200
2025-07-13 16:31:56,889 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 16:31:56,889 - yfinance - DEBUG -   Exiting get()
2025-07-13 16:31:56,899 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 16:31:56,903 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 16:31:56,908 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 16:31:56,913 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 16:31:56,913 - yfinance - DEBUG -  Exiting history()
2025-07-13 16:31:56,913 - yfinance - DEBUG - Exiting history()
2025-07-13 16:31:56,913 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 16:31:56,915 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 16:31:56,915 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 16:31:56,919 - DataHandler - INFO - Data quality validation for EURUSD: Excellent (Score: 99.6)
2025-07-13 16:31:56,919 - BacktestEngine - INFO - Data quality for EURUSD: Excellent (Score: 99.6)
2025-07-13 16:31:56,919 - DataHandler - DEBUG - Created Backtrader feed for EURUSD
2025-07-13 16:31:56,919 - BacktestEngine - INFO - Successfully loaded 254 bars for EURUSD
2025-07-13 16:31:56,919 - BacktestEngine - ERROR - Error running backtest: 'NoneType' object has no attribute 'addindicator'
2025-07-13 16:31:56,919 - __main__ - ERROR - Backtest execution failed
2025-07-13 16:31:56,919 - __main__ - ERROR - Backtest failed
2025-07-13 17:49:48,151 - __main__ - INFO - Forex Backtesting System started
2025-07-13 17:49:48,154 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 17:49:48,154 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 17:49:48,154 - __main__ - INFO - Starting backtest execution
2025-07-13 17:49:48,154 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 17:49:48,154 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 17:49:48,154 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 17:49:48,154 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 17:49:48,154 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 17:49:48,154 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 17:49:48,154 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 17:49:48,154 - yfinance - DEBUG - Entering history()
2025-07-13 17:49:48,158 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 17:49:48,158 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 17:49:48,172 - yfinance - DEBUG -  Entering history()
2025-07-13 17:49:48,174 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 17:49:48,175 - yfinance - DEBUG -   Entering get()
2025-07-13 17:49:48,175 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 17:49:48,175 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 17:49:48,175 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 17:49:48,175 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 17:49:48,175 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 17:49:48,175 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 17:49:48,175 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 17:49:48,175 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 17:49:48,175 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 17:49:48,176 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 17:49:48,176 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 17:49:48,176 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 17:49:48,176 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 17:49:48,176 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 17:49:48,176 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 17:49:48,176 - yfinance - DEBUG - reusing cookie
2025-07-13 17:49:48,176 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 17:49:48,507 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 17:49:48,507 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 17:49:48,507 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 17:49:48,507 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 17:49:48,648 - yfinance - DEBUG - response code=200
2025-07-13 17:49:48,649 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 17:49:48,649 - yfinance - DEBUG -   Exiting get()
2025-07-13 17:49:48,657 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 17:49:48,662 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:49:48,669 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:49:48,677 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:49:48,678 - yfinance - DEBUG -  Exiting history()
2025-07-13 17:49:48,678 - yfinance - DEBUG - Exiting history()
2025-07-13 17:49:48,679 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 17:49:48,681 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 17:49:48,682 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 17:49:48,685 - DataHandler - INFO - Data quality validation for EURUSD: Excellent (Score: 99.6)
2025-07-13 17:49:48,685 - BacktestEngine - INFO - Data quality for EURUSD: Excellent (Score: 99.6)
2025-07-13 17:49:48,685 - DataHandler - DEBUG - Created Backtrader feed for EURUSD
2025-07-13 17:49:48,685 - BacktestEngine - INFO - Successfully loaded 254 bars for EURUSD
2025-07-13 17:49:48,685 - BacktestEngine - ERROR - Error running backtest: 'NoneType' object has no attribute 'addindicator'
2025-07-13 17:49:48,685 - __main__ - ERROR - Backtest execution failed
2025-07-13 17:49:48,685 - __main__ - ERROR - Backtest failed
2025-07-13 17:51:09,006 - __main__ - INFO - Forex Backtesting System started
2025-07-13 17:51:09,007 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 17:51:09,007 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 17:51:09,007 - __main__ - INFO - Starting backtest execution
2025-07-13 17:51:09,007 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 17:51:09,007 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 17:51:09,007 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 17:51:09,007 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 17:51:09,007 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 17:51:09,007 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 17:51:09,007 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 17:51:09,008 - yfinance - DEBUG - Entering history()
2025-07-13 17:51:09,015 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 17:51:09,016 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 17:51:09,032 - yfinance - DEBUG -  Entering history()
2025-07-13 17:51:09,035 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 17:51:09,036 - yfinance - DEBUG -   Entering get()
2025-07-13 17:51:09,036 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 17:51:09,036 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 17:51:09,036 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 17:51:09,036 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 17:51:09,036 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 17:51:09,036 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 17:51:09,036 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 17:51:09,036 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 17:51:09,037 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 17:51:09,037 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 17:51:09,038 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 17:51:09,038 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 17:51:09,038 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 17:51:09,038 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 17:51:09,038 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 17:51:09,038 - yfinance - DEBUG - reusing cookie
2025-07-13 17:51:09,038 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 17:51:09,390 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 17:51:09,390 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 17:51:09,390 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 17:51:09,390 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 17:51:09,529 - yfinance - DEBUG - response code=200
2025-07-13 17:51:09,529 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 17:51:09,529 - yfinance - DEBUG -   Exiting get()
2025-07-13 17:51:09,538 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 17:51:09,541 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:51:09,548 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:51:09,552 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:51:09,552 - yfinance - DEBUG -  Exiting history()
2025-07-13 17:51:09,552 - yfinance - DEBUG - Exiting history()
2025-07-13 17:51:09,553 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 17:51:09,556 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 17:51:09,556 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 17:51:09,559 - DataHandler - INFO - Data quality validation for EURUSD: Excellent (Score: 99.6)
2025-07-13 17:51:09,559 - BacktestEngine - INFO - Data quality for EURUSD: Excellent (Score: 99.6)
2025-07-13 17:51:09,559 - DataHandler - DEBUG - Created Backtrader feed for EURUSD
2025-07-13 17:51:09,559 - BacktestEngine - INFO - Successfully loaded 254 bars for EURUSD
2025-07-13 17:51:09,559 - BacktestEngine - ERROR - Error running backtest: 'NoneType' object has no attribute 'addindicator'
2025-07-13 17:51:09,559 - __main__ - ERROR - Backtest execution failed
2025-07-13 17:51:09,559 - __main__ - ERROR - Backtest failed
2025-07-13 17:52:19,292 - __main__ - INFO - Forex Backtesting System started
2025-07-13 17:52:19,294 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 17:52:19,294 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 17:52:19,294 - __main__ - INFO - Starting backtest execution
2025-07-13 17:52:19,294 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 17:52:19,294 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 17:52:19,294 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 17:52:19,294 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 17:52:19,294 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 17:52:19,294 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 17:52:19,294 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 17:52:19,294 - yfinance - DEBUG - Entering history()
2025-07-13 17:52:19,300 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 17:52:19,300 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 17:52:19,315 - yfinance - DEBUG -  Entering history()
2025-07-13 17:52:19,317 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 17:52:19,317 - yfinance - DEBUG -   Entering get()
2025-07-13 17:52:19,317 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 17:52:19,317 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 17:52:19,317 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 17:52:19,317 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 17:52:19,317 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 17:52:19,317 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 17:52:19,317 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 17:52:19,317 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 17:52:19,318 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 17:52:19,318 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 17:52:19,319 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 17:52:19,319 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 17:52:19,319 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 17:52:19,319 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 17:52:19,319 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 17:52:19,319 - yfinance - DEBUG - reusing cookie
2025-07-13 17:52:19,319 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 17:52:19,651 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 17:52:19,652 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 17:52:19,652 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 17:52:19,652 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 17:52:19,814 - yfinance - DEBUG - response code=200
2025-07-13 17:52:19,814 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 17:52:19,815 - yfinance - DEBUG -   Exiting get()
2025-07-13 17:52:19,827 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 17:52:19,830 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:52:19,837 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:52:19,844 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:52:19,844 - yfinance - DEBUG -  Exiting history()
2025-07-13 17:52:19,844 - yfinance - DEBUG - Exiting history()
2025-07-13 17:52:19,846 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 17:52:19,848 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 17:52:19,848 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 17:52:19,851 - DataHandler - INFO - Data quality validation for EURUSD: Excellent (Score: 99.6)
2025-07-13 17:52:19,851 - BacktestEngine - INFO - Data quality for EURUSD: Excellent (Score: 99.6)
2025-07-13 17:52:19,851 - DataHandler - DEBUG - Created Backtrader feed for EURUSD
2025-07-13 17:52:19,851 - BacktestEngine - INFO - Successfully loaded 254 bars for EURUSD
2025-07-13 17:52:19,852 - BacktestEngine - ERROR - Error running backtest: 'NoneType' object has no attribute 'addindicator'
2025-07-13 17:52:19,852 - __main__ - ERROR - Backtest execution failed
2025-07-13 17:52:19,852 - __main__ - ERROR - Backtest failed
2025-07-13 17:52:57,273 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 17:52:57,274 - yfinance - DEBUG - Entering history()
2025-07-13 17:52:57,280 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 17:52:57,281 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 17:52:57,295 - yfinance - DEBUG -  Entering history()
2025-07-13 17:52:57,298 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 17:52:57,298 - yfinance - DEBUG -   Entering get()
2025-07-13 17:52:57,298 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 17:52:57,299 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 17:52:57,299 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 17:52:57,299 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 17:52:57,299 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 17:52:57,299 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 17:52:57,299 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 17:52:57,299 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 17:52:57,299 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 17:52:57,300 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 17:52:57,300 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 17:52:57,300 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 17:52:57,300 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 17:52:57,300 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 17:52:57,300 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 17:52:57,300 - yfinance - DEBUG - reusing cookie
2025-07-13 17:52:57,300 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 17:52:57,636 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 17:52:57,636 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 17:52:57,636 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 17:52:57,636 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 17:52:57,790 - yfinance - DEBUG - response code=200
2025-07-13 17:52:57,790 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 17:52:57,790 - yfinance - DEBUG -   Exiting get()
2025-07-13 17:52:57,802 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 17:52:57,806 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:52:57,814 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:52:57,821 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:52:57,821 - yfinance - DEBUG -  Exiting history()
2025-07-13 17:52:57,821 - yfinance - DEBUG - Exiting history()
2025-07-13 17:52:57,822 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 17:52:57,825 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 17:52:57,825 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 17:52:57,843 - SimpleWorkingStrategy - INFO - Simple strategy initialized
2025-07-13 17:52:57,846 - SimpleWorkingStrategy - INFO - Buy signal at 1.08388
2025-07-13 17:52:57,847 - SimpleWorkingStrategy - INFO - Sell signal at 1.08324
2025-07-13 17:52:57,848 - SimpleWorkingStrategy - INFO - Buy signal at 1.07469
2025-07-13 17:52:57,848 - SimpleWorkingStrategy - INFO - Sell signal at 1.07403
2025-07-13 17:52:57,849 - SimpleWorkingStrategy - INFO - Buy signal at 1.08260
2025-07-13 17:52:57,850 - SimpleWorkingStrategy - INFO - Sell signal at 1.07876
2025-07-13 17:52:57,850 - SimpleWorkingStrategy - INFO - Buy signal at 1.09108
2025-07-13 17:52:57,852 - SimpleWorkingStrategy - INFO - Sell signal at 1.10356
2025-07-13 17:53:32,224 - __main__ - INFO - Forex Backtesting System started
2025-07-13 17:53:32,224 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 17:53:32,224 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 17:53:32,224 - __main__ - INFO - Starting backtest execution
2025-07-13 17:53:32,225 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 17:53:32,225 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 17:53:32,225 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 17:53:32,225 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 17:53:32,225 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 17:53:32,225 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 17:53:32,225 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 17:53:32,225 - yfinance - DEBUG - Entering history()
2025-07-13 17:53:32,230 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 17:53:32,230 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 17:53:32,245 - yfinance - DEBUG -  Entering history()
2025-07-13 17:53:32,252 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 17:53:32,253 - yfinance - DEBUG -   Entering get()
2025-07-13 17:53:32,253 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 17:53:32,253 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 17:53:32,254 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 17:53:32,254 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 17:53:32,254 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 17:53:32,254 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 17:53:32,254 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 17:53:32,254 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 17:53:32,255 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 17:53:32,255 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 17:53:32,255 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 17:53:32,255 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 17:53:32,255 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 17:53:32,255 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 17:53:32,255 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 17:53:32,255 - yfinance - DEBUG - reusing cookie
2025-07-13 17:53:32,255 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 17:53:32,583 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 17:53:32,584 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 17:53:32,585 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 17:53:32,585 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 17:53:32,731 - yfinance - DEBUG - response code=200
2025-07-13 17:53:32,732 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 17:53:32,732 - yfinance - DEBUG -   Exiting get()
2025-07-13 17:53:32,757 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 17:53:32,764 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:53:32,775 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:53:32,780 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:53:32,780 - yfinance - DEBUG -  Exiting history()
2025-07-13 17:53:32,780 - yfinance - DEBUG - Exiting history()
2025-07-13 17:53:32,781 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 17:53:32,783 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 17:53:32,783 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 17:53:32,786 - DataHandler - INFO - Data quality validation for EURUSD: Excellent (Score: 99.6)
2025-07-13 17:53:32,786 - BacktestEngine - INFO - Data quality for EURUSD: Excellent (Score: 99.6)
2025-07-13 17:53:32,787 - DataHandler - DEBUG - Created Backtrader feed for EURUSD
2025-07-13 17:53:32,787 - BacktestEngine - INFO - Successfully loaded 254 bars for EURUSD
2025-07-13 17:53:32,787 - BacktestEngine - ERROR - Error running backtest: 'NoneType' object has no attribute 'addindicator'
2025-07-13 17:53:32,787 - __main__ - ERROR - Backtest execution failed
2025-07-13 17:53:32,787 - __main__ - ERROR - Backtest failed
2025-07-13 17:54:34,832 - __main__ - INFO - Forex Backtesting System started
2025-07-13 17:54:34,832 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 17:54:34,832 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 17:54:34,832 - __main__ - INFO - Starting backtest execution
2025-07-13 17:54:34,833 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 17:54:34,833 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 17:54:34,833 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 17:54:34,833 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 17:54:34,833 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 17:54:34,833 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 17:54:34,833 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 17:54:34,833 - yfinance - DEBUG - Entering history()
2025-07-13 17:54:34,839 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 17:54:34,839 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 17:54:34,855 - yfinance - DEBUG -  Entering history()
2025-07-13 17:54:34,858 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 17:54:34,859 - yfinance - DEBUG -   Entering get()
2025-07-13 17:54:34,859 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 17:54:34,859 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 17:54:34,859 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 17:54:34,859 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 17:54:34,859 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 17:54:34,859 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 17:54:34,859 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 17:54:34,859 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 17:54:34,860 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 17:54:34,860 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 17:54:34,860 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 17:54:34,861 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 17:54:34,861 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 17:54:34,861 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 17:54:34,861 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 17:54:34,861 - yfinance - DEBUG - reusing cookie
2025-07-13 17:54:34,861 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 17:54:35,584 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 17:54:35,585 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 17:54:35,585 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 17:54:35,585 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 17:54:35,733 - yfinance - DEBUG - response code=200
2025-07-13 17:54:35,733 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 17:54:35,733 - yfinance - DEBUG -   Exiting get()
2025-07-13 17:54:35,745 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 17:54:35,749 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:54:35,757 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:54:35,762 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:54:35,763 - yfinance - DEBUG -  Exiting history()
2025-07-13 17:54:35,763 - yfinance - DEBUG - Exiting history()
2025-07-13 17:54:35,764 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 17:54:35,766 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 17:54:35,766 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 17:54:35,769 - DataHandler - INFO - Data quality validation for EURUSD: Excellent (Score: 99.6)
2025-07-13 17:54:35,769 - BacktestEngine - INFO - Data quality for EURUSD: Excellent (Score: 99.6)
2025-07-13 17:54:35,769 - DataHandler - DEBUG - Created Backtrader feed for EURUSD
2025-07-13 17:54:35,769 - BacktestEngine - INFO - Successfully loaded 254 bars for EURUSD
2025-07-13 17:54:35,769 - BacktestEngine - ERROR - Error running backtest: 'NoneType' object has no attribute 'addindicator'
2025-07-13 17:54:35,769 - __main__ - ERROR - Backtest execution failed
2025-07-13 17:54:35,769 - __main__ - ERROR - Backtest failed
2025-07-13 17:55:14,155 - __main__ - INFO - Forex Backtesting System started
2025-07-13 17:55:14,156 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 17:55:14,156 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 17:55:14,156 - __main__ - INFO - Starting backtest execution
2025-07-13 17:55:14,156 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 17:55:14,156 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 17:55:14,156 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 17:55:14,157 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 17:55:14,157 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 17:55:14,157 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 17:55:14,157 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 17:55:14,157 - yfinance - DEBUG - Entering history()
2025-07-13 17:55:14,162 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 17:55:14,163 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 17:55:14,178 - yfinance - DEBUG -  Entering history()
2025-07-13 17:55:14,182 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 17:55:14,184 - yfinance - DEBUG -   Entering get()
2025-07-13 17:55:14,184 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 17:55:14,184 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 17:55:14,184 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 17:55:14,184 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 17:55:14,184 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 17:55:14,184 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 17:55:14,184 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 17:55:14,184 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 17:55:14,185 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 17:55:14,185 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 17:55:14,185 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 17:55:14,185 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 17:55:14,186 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 17:55:14,186 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 17:55:14,186 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 17:55:14,186 - yfinance - DEBUG - reusing cookie
2025-07-13 17:55:14,186 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 17:55:14,531 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 17:55:14,532 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 17:55:14,532 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 17:55:14,532 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 17:55:14,696 - yfinance - DEBUG - response code=200
2025-07-13 17:55:14,697 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 17:55:14,697 - yfinance - DEBUG -   Exiting get()
2025-07-13 17:55:14,713 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 17:55:14,717 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:55:14,726 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:55:14,731 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:55:14,731 - yfinance - DEBUG -  Exiting history()
2025-07-13 17:55:14,731 - yfinance - DEBUG - Exiting history()
2025-07-13 17:55:14,732 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 17:55:14,734 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 17:55:14,734 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 17:55:14,736 - DataHandler - INFO - Data quality validation for EURUSD: Excellent (Score: 99.6)
2025-07-13 17:55:14,736 - BacktestEngine - INFO - Data quality for EURUSD: Excellent (Score: 99.6)
2025-07-13 17:55:14,737 - DataHandler - DEBUG - Created Backtrader feed for EURUSD
2025-07-13 17:55:14,737 - BacktestEngine - INFO - Successfully loaded 254 bars for EURUSD
2025-07-13 17:55:14,737 - BacktestEngine - ERROR - Error running backtest: 'NoneType' object has no attribute 'addindicator'
2025-07-13 17:55:14,737 - __main__ - ERROR - Backtest execution failed
2025-07-13 17:55:14,737 - __main__ - ERROR - Backtest failed
2025-07-13 17:55:56,232 - __main__ - INFO - Forex Backtesting System started
2025-07-13 17:55:56,233 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 17:55:56,233 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 17:55:56,233 - __main__ - INFO - Starting backtest execution
2025-07-13 17:55:56,233 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 17:55:56,233 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 17:55:56,233 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 17:55:56,233 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 17:55:56,233 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 17:55:56,233 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 17:55:56,233 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 17:55:56,234 - yfinance - DEBUG - Entering history()
2025-07-13 17:55:56,239 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 17:55:56,239 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 17:55:56,253 - yfinance - DEBUG -  Entering history()
2025-07-13 17:55:56,255 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 17:55:56,256 - yfinance - DEBUG -   Entering get()
2025-07-13 17:55:56,256 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 17:55:56,256 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 17:55:56,256 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 17:55:56,256 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 17:55:56,256 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 17:55:56,256 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 17:55:56,256 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 17:55:56,256 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 17:55:56,257 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 17:55:56,258 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 17:55:56,258 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 17:55:56,258 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 17:55:56,258 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 17:55:56,258 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 17:55:56,258 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 17:55:56,258 - yfinance - DEBUG - reusing cookie
2025-07-13 17:55:56,258 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 17:55:56,606 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 17:55:56,606 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 17:55:56,606 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 17:55:56,606 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 17:55:56,770 - yfinance - DEBUG - response code=200
2025-07-13 17:55:56,770 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 17:55:56,770 - yfinance - DEBUG -   Exiting get()
2025-07-13 17:55:56,777 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 17:55:56,780 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:55:56,784 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:55:56,788 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:55:56,788 - yfinance - DEBUG -  Exiting history()
2025-07-13 17:55:56,788 - yfinance - DEBUG - Exiting history()
2025-07-13 17:55:56,789 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 17:55:56,790 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 17:55:56,790 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 17:55:56,792 - DataHandler - INFO - Data quality validation for EURUSD: Excellent (Score: 99.6)
2025-07-13 17:55:56,792 - BacktestEngine - INFO - Data quality for EURUSD: Excellent (Score: 99.6)
2025-07-13 17:55:56,792 - DataHandler - DEBUG - Created Backtrader feed for EURUSD
2025-07-13 17:55:56,792 - BacktestEngine - INFO - Successfully loaded 254 bars for EURUSD
2025-07-13 17:55:56,792 - BacktestEngine - ERROR - Error running backtest: 'NoneType' object has no attribute 'addindicator'
2025-07-13 17:55:56,792 - __main__ - ERROR - Backtest execution failed
2025-07-13 17:55:56,792 - __main__ - ERROR - Backtest failed
2025-07-13 17:57:15,438 - __main__ - INFO - Forex Backtesting System started
2025-07-13 17:57:15,440 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 17:57:15,440 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 17:57:15,440 - __main__ - INFO - Starting backtest execution
2025-07-13 17:57:15,440 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 17:57:15,441 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 17:57:15,441 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 17:57:15,441 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 17:57:15,441 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 17:57:15,441 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 17:57:15,441 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 17:57:15,441 - yfinance - DEBUG - Entering history()
2025-07-13 17:57:15,446 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 17:57:15,447 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 17:57:15,461 - yfinance - DEBUG -  Entering history()
2025-07-13 17:57:15,464 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 17:57:15,465 - yfinance - DEBUG -   Entering get()
2025-07-13 17:57:15,465 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 17:57:15,465 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 17:57:15,465 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 17:57:15,465 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 17:57:15,465 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 17:57:15,465 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 17:57:15,465 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 17:57:15,465 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 17:57:15,466 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 17:57:15,466 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 17:57:15,466 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 17:57:15,466 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 17:57:15,466 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 17:57:15,466 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 17:57:15,466 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 17:57:15,466 - yfinance - DEBUG - reusing cookie
2025-07-13 17:57:15,466 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 17:57:15,794 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 17:57:15,794 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 17:57:15,795 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 17:57:15,795 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 17:57:15,945 - yfinance - DEBUG - response code=200
2025-07-13 17:57:15,946 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 17:57:15,946 - yfinance - DEBUG -   Exiting get()
2025-07-13 17:57:15,957 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 17:57:15,960 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:57:15,966 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:57:15,977 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:57:15,978 - yfinance - DEBUG -  Exiting history()
2025-07-13 17:57:15,978 - yfinance - DEBUG - Exiting history()
2025-07-13 17:57:15,979 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 17:57:15,993 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 17:57:15,993 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 17:57:15,997 - DataHandler - INFO - Data quality validation for EURUSD: Excellent (Score: 99.6)
2025-07-13 17:57:15,997 - BacktestEngine - INFO - Data quality for EURUSD: Excellent (Score: 99.6)
2025-07-13 17:57:15,998 - DataHandler - DEBUG - Created Backtrader feed for EURUSD
2025-07-13 17:57:15,998 - BacktestEngine - INFO - Successfully loaded 254 bars for EURUSD
2025-07-13 17:57:15,998 - BacktestEngine - ERROR - Error running backtest: 'NoneType' object has no attribute 'addindicator'
2025-07-13 17:57:15,999 - __main__ - ERROR - Backtest execution failed
2025-07-13 17:57:15,999 - __main__ - ERROR - Backtest failed
2025-07-13 17:58:06,978 - __main__ - INFO - Forex Backtesting System started
2025-07-13 17:58:06,979 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 17:58:06,979 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 17:58:06,979 - __main__ - INFO - Starting backtest execution
2025-07-13 17:58:06,980 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 17:58:06,980 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 17:58:06,980 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 17:58:06,980 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 17:58:06,980 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 17:58:06,980 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 17:58:06,980 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 17:58:06,980 - yfinance - DEBUG - Entering history()
2025-07-13 17:58:06,986 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 17:58:06,986 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 17:58:07,001 - yfinance - DEBUG -  Entering history()
2025-07-13 17:58:07,004 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 17:58:07,005 - yfinance - DEBUG -   Entering get()
2025-07-13 17:58:07,005 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 17:58:07,005 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 17:58:07,005 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 17:58:07,005 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 17:58:07,005 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 17:58:07,005 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 17:58:07,005 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 17:58:07,005 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 17:58:07,006 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 17:58:07,006 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 17:58:07,006 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 17:58:07,006 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 17:58:07,007 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 17:58:07,007 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 17:58:07,007 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 17:58:07,007 - yfinance - DEBUG - reusing cookie
2025-07-13 17:58:07,007 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 17:58:07,342 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 17:58:07,343 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 17:58:07,344 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 17:58:07,344 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 17:58:07,483 - yfinance - DEBUG - response code=200
2025-07-13 17:58:07,483 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 17:58:07,483 - yfinance - DEBUG -   Exiting get()
2025-07-13 17:58:07,495 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 17:58:07,499 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:58:07,508 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:58:07,514 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:58:07,515 - yfinance - DEBUG -  Exiting history()
2025-07-13 17:58:07,515 - yfinance - DEBUG - Exiting history()
2025-07-13 17:58:07,516 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 17:58:07,520 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 17:58:07,520 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 17:58:07,523 - DataHandler - INFO - Data quality validation for EURUSD: Excellent (Score: 99.6)
2025-07-13 17:58:07,523 - BacktestEngine - INFO - Data quality for EURUSD: Excellent (Score: 99.6)
2025-07-13 17:58:07,524 - DataHandler - DEBUG - Created Backtrader feed for EURUSD
2025-07-13 17:58:07,524 - BacktestEngine - INFO - Successfully loaded 254 bars for EURUSD
2025-07-13 17:58:07,524 - BacktestEngine - ERROR - Error running backtest: 'NoneType' object has no attribute 'addindicator'
2025-07-13 17:58:07,524 - __main__ - ERROR - Backtest execution failed
2025-07-13 17:58:07,524 - __main__ - ERROR - Backtest failed
2025-07-13 17:58:16,793 - __main__ - INFO - Forex Backtesting System started
2025-07-13 17:58:16,793 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 17:58:16,793 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 17:58:16,794 - __main__ - INFO - Starting backtest execution
2025-07-13 17:58:16,794 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 17:58:16,794 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 17:58:16,794 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 17:58:16,794 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 17:58:16,794 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 17:58:16,794 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 17:58:16,794 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 17:58:16,794 - yfinance - DEBUG - Entering history()
2025-07-13 17:58:16,795 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 17:58:16,796 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 17:58:16,808 - yfinance - DEBUG -  Entering history()
2025-07-13 17:58:16,810 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 17:58:16,810 - yfinance - DEBUG -   Entering get()
2025-07-13 17:58:16,810 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 17:58:16,810 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 17:58:16,810 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 17:58:16,810 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 17:58:16,810 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 17:58:16,810 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 17:58:16,810 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 17:58:16,810 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 17:58:16,811 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 17:58:16,812 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 17:58:16,812 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 17:58:16,812 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 17:58:16,812 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 17:58:16,812 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 17:58:16,812 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 17:58:16,812 - yfinance - DEBUG - reusing cookie
2025-07-13 17:58:16,812 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 17:58:17,124 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 17:58:17,125 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 17:58:17,125 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 17:58:17,125 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 17:58:17,258 - yfinance - DEBUG - response code=200
2025-07-13 17:58:17,258 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 17:58:17,258 - yfinance - DEBUG -   Exiting get()
2025-07-13 17:58:17,261 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 17:58:17,261 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:58:17,265 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:58:17,267 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:58:17,267 - yfinance - DEBUG -  Exiting history()
2025-07-13 17:58:17,267 - yfinance - DEBUG - Exiting history()
2025-07-13 17:58:17,268 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 17:58:17,268 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 17:58:17,268 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 17:58:17,270 - DataHandler - INFO - Data quality validation for EURUSD: Excellent (Score: 99.6)
2025-07-13 17:58:17,270 - BacktestEngine - INFO - Data quality for EURUSD: Excellent (Score: 99.6)
2025-07-13 17:58:17,270 - DataHandler - DEBUG - Created Backtrader feed for EURUSD
2025-07-13 17:58:17,270 - BacktestEngine - INFO - Successfully loaded 254 bars for EURUSD
2025-07-13 17:58:17,270 - BacktestEngine - ERROR - Error running backtest: 'NoneType' object has no attribute 'addindicator'
2025-07-13 17:58:17,270 - __main__ - ERROR - Backtest execution failed
2025-07-13 17:58:17,270 - __main__ - ERROR - Backtest failed
2025-07-13 17:58:57,909 - __main__ - INFO - Forex Backtesting System started
2025-07-13 17:58:57,912 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 17:58:57,912 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 17:58:57,912 - __main__ - INFO - Starting backtest execution
2025-07-13 17:58:57,912 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 17:58:57,912 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 17:58:57,912 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 17:58:57,912 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 17:58:57,912 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 17:58:57,912 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 17:58:57,912 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 17:58:57,913 - yfinance - DEBUG - Entering history()
2025-07-13 17:58:57,918 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 17:58:57,919 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 17:58:57,937 - yfinance - DEBUG -  Entering history()
2025-07-13 17:58:57,940 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 17:58:57,941 - yfinance - DEBUG -   Entering get()
2025-07-13 17:58:57,941 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 17:58:57,941 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 17:58:57,941 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 17:58:57,941 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 17:58:57,941 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 17:58:57,941 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 17:58:57,941 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 17:58:57,942 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 17:58:57,943 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 17:58:57,943 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 17:58:57,943 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 17:58:57,943 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 17:58:57,943 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 17:58:57,943 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 17:58:57,943 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 17:58:57,943 - yfinance - DEBUG - reusing cookie
2025-07-13 17:58:57,943 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 17:58:58,284 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 17:58:58,285 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 17:58:58,285 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 17:58:58,285 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 17:58:58,434 - yfinance - DEBUG - response code=200
2025-07-13 17:58:58,434 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 17:58:58,435 - yfinance - DEBUG -   Exiting get()
2025-07-13 17:58:58,444 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 17:58:58,446 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:58:58,451 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:58:58,454 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 17:58:58,454 - yfinance - DEBUG -  Exiting history()
2025-07-13 17:58:58,454 - yfinance - DEBUG - Exiting history()
2025-07-13 17:58:58,455 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 17:58:58,457 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 17:58:58,457 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 17:58:58,459 - DataHandler - INFO - Data quality validation for EURUSD: Excellent (Score: 99.6)
2025-07-13 17:58:58,459 - BacktestEngine - INFO - Data quality for EURUSD: Excellent (Score: 99.6)
2025-07-13 17:58:58,459 - DataHandler - DEBUG - Created Backtrader feed for EURUSD
2025-07-13 17:58:58,460 - BacktestEngine - INFO - Successfully loaded 254 bars for EURUSD
2025-07-13 17:58:58,460 - BacktestEngine - ERROR - Error running backtest: 'NoneType' object has no attribute 'addindicator'
2025-07-13 17:58:58,462 - BacktestEngine - DEBUG - Full traceback: Traceback (most recent call last):
  File "/Users/<USER>/Library/CloudStorage/OneDrive-BluebikGroup/Private_Folder/FOREX/venv4/backtest_engine.py", line 246, in run_backtest
    if data_feed:
       ^^^^^^^^^
  File "/Users/<USER>/Library/CloudStorage/OneDrive-BluebikGroup/Private_Folder/FOREX/venv4/.venv/lib/python3.13/site-packages/backtrader/lineroot.py", line 287, in __nonzero__
    return self._operationown(bool)
           ~~~~~~~~~~~~~~~~~~^^^^^^
  File "/Users/<USER>/Library/CloudStorage/OneDrive-BluebikGroup/Private_Folder/FOREX/venv4/.venv/lib/python3.13/site-packages/backtrader/lineroot.py", line 92, in _operationown
    return self._operationown_stage1(operation)
           ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "/Users/<USER>/Library/CloudStorage/OneDrive-BluebikGroup/Private_Folder/FOREX/venv4/.venv/lib/python3.13/site-packages/backtrader/lineroot.py", line 184, in _operationown_stage1
    return self._makeoperationown(operation, _ownerskip=self)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/CloudStorage/OneDrive-BluebikGroup/Private_Folder/FOREX/venv4/.venv/lib/python3.13/site-packages/backtrader/lineroot.py", line 334, in _makeoperationown
    return self.lines[0]._makeoperationown(operation, _ownerskip)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/CloudStorage/OneDrive-BluebikGroup/Private_Folder/FOREX/venv4/.venv/lib/python3.13/site-packages/backtrader/linebuffer.py", line 381, in _makeoperationown
    return LineOwnOperation(self, operation, _ownerskip=_ownerskip)
  File "/Users/<USER>/Library/CloudStorage/OneDrive-BluebikGroup/Private_Folder/FOREX/venv4/.venv/lib/python3.13/site-packages/backtrader/linebuffer.py", line 522, in __call__
    return super(MetaLineActions, cls).__call__(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/CloudStorage/OneDrive-BluebikGroup/Private_Folder/FOREX/venv4/.venv/lib/python3.13/site-packages/backtrader/metabase.py", line 89, in __call__
    _obj, args, kwargs = cls.dopostinit(_obj, *args, **kwargs)
                         ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/CloudStorage/OneDrive-BluebikGroup/Private_Folder/FOREX/venv4/.venv/lib/python3.13/site-packages/backtrader/linebuffer.py", line 566, in dopostinit
    _obj._owner.addindicator(_obj)
    ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'addindicator'

2025-07-13 17:58:58,462 - __main__ - ERROR - Backtest execution failed
2025-07-13 17:58:58,462 - __main__ - ERROR - Backtest failed
2025-07-13 18:44:12,308 - __main__ - INFO - Forex Backtesting System started
2025-07-13 18:44:12,309 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 18:44:12,309 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 18:44:12,309 - __main__ - INFO - Starting backtest execution
2025-07-13 18:44:12,309 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 18:44:12,309 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 18:44:12,309 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 18:44:12,309 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 18:44:12,309 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 18:44:12,310 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 18:44:12,310 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 18:44:12,310 - yfinance - DEBUG - Entering history()
2025-07-13 18:44:12,316 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 18:44:12,316 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 18:44:12,351 - yfinance - DEBUG -  Entering history()
2025-07-13 18:44:12,354 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 18:44:12,355 - yfinance - DEBUG -   Entering get()
2025-07-13 18:44:12,355 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 18:44:12,355 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 18:44:12,355 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 18:44:12,355 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 18:44:12,355 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 18:44:12,355 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 18:44:12,355 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 18:44:12,355 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 18:44:12,358 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 18:44:12,358 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 18:44:12,358 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 18:44:12,358 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 18:44:12,358 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 18:44:12,358 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 18:44:12,358 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 18:44:12,358 - yfinance - DEBUG - reusing cookie
2025-07-13 18:44:12,358 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 18:44:12,762 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 18:44:12,764 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 18:44:12,764 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 18:44:12,764 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 18:44:13,022 - yfinance - DEBUG - response code=200
2025-07-13 18:44:13,022 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 18:44:13,022 - yfinance - DEBUG -   Exiting get()
2025-07-13 18:44:13,037 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 18:44:13,040 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 18:44:13,051 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 18:44:13,055 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 18:44:13,056 - yfinance - DEBUG -  Exiting history()
2025-07-13 18:44:13,056 - yfinance - DEBUG - Exiting history()
2025-07-13 18:44:13,056 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 18:44:13,059 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 18:44:13,059 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 18:44:13,061 - DataHandler - INFO - Data quality validation for EURUSD: Excellent (Score: 99.6)
2025-07-13 18:44:13,061 - BacktestEngine - INFO - Data quality for EURUSD: Excellent (Score: 99.6)
2025-07-13 18:44:13,062 - DataHandler - DEBUG - Created Backtrader feed for EURUSD
2025-07-13 18:44:13,062 - BacktestEngine - INFO - Successfully loaded 254 bars for EURUSD
2025-07-13 18:44:13,062 - BacktestEngine - DEBUG - About to add strategy
2025-07-13 18:44:13,062 - BacktestEngine - INFO - Adding strategy: ema_crossover
2025-07-13 18:44:13,062 - BacktestEngine - DEBUG - Adding strategy <class 'strategy.minimal_strategy.MinimalStrategy'> with params: {'ema_fast': 55, 'ema_slow': 89}
2025-07-13 18:44:13,062 - BacktestEngine - INFO - Strategy ema_crossover added successfully
2025-07-13 18:44:13,062 - BacktestEngine - DEBUG - Strategy added successfully
2025-07-13 18:44:13,062 - BacktestEngine - INFO - Starting backtest with 1 symbols
2025-07-13 18:44:13,062 - BacktestEngine - INFO - Initial portfolio value: $10,000.00
2025-07-13 18:44:13,062 - BacktestEngine - DEBUG - About to run cerebro.run()
2025-07-13 18:44:13,095 - BacktestEngine - ERROR - Error compiling results: 'NoneType' object has no attribute 'addindicator'
2025-07-13 18:44:13,095 - BacktestEngine - INFO - Backtest completed successfully
2025-07-13 18:44:13,095 - BacktestEngine - INFO - Final portfolio value: $9,884.44
2025-07-13 18:44:13,095 - BacktestEngine - ERROR - Error running backtest: 'total_return'
2025-07-13 18:44:13,096 - BacktestEngine - DEBUG - Full traceback: Traceback (most recent call last):
  File "/Users/<USER>/Library/CloudStorage/OneDrive-BluebikGroup/Private_Folder/FOREX/venv4/backtest_engine.py", line 292, in run_backtest
    self.logger.info(f"Total return: {backtest_results['total_return']:.2%}")
                                      ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 'total_return'

2025-07-13 18:44:13,096 - __main__ - ERROR - Backtest execution failed
2025-07-13 18:44:13,096 - __main__ - ERROR - Backtest failed
2025-07-13 18:46:50,735 - __main__ - INFO - Forex Backtesting System started
2025-07-13 18:46:50,740 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 18:46:50,740 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 18:46:50,740 - __main__ - INFO - Starting backtest execution
2025-07-13 18:46:50,740 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 18:46:50,740 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 18:46:50,740 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 18:46:50,740 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 18:46:50,740 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 18:46:50,740 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 18:46:50,740 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 18:46:50,741 - yfinance - DEBUG - Entering history()
2025-07-13 18:46:50,746 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 18:46:50,746 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 18:46:50,763 - yfinance - DEBUG -  Entering history()
2025-07-13 18:46:50,767 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 18:46:50,768 - yfinance - DEBUG -   Entering get()
2025-07-13 18:46:50,768 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 18:46:50,768 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 18:46:50,768 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 18:46:50,768 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 18:46:50,768 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 18:46:50,768 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 18:46:50,768 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 18:46:50,768 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 18:46:50,769 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 18:46:50,769 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 18:46:50,769 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 18:46:50,769 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 18:46:50,769 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 18:46:50,769 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 18:46:50,769 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 18:46:50,769 - yfinance - DEBUG - reusing cookie
2025-07-13 18:46:50,769 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 18:46:51,153 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 18:46:51,154 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 18:46:51,154 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 18:46:51,154 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 18:46:51,294 - yfinance - DEBUG - response code=200
2025-07-13 18:46:51,295 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 18:46:51,295 - yfinance - DEBUG -   Exiting get()
2025-07-13 18:46:51,308 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 18:46:51,312 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 18:46:51,319 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 18:46:51,324 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 18:46:51,324 - yfinance - DEBUG -  Exiting history()
2025-07-13 18:46:51,324 - yfinance - DEBUG - Exiting history()
2025-07-13 18:46:51,325 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 18:46:51,327 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 18:46:51,328 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 18:46:51,330 - DataHandler - INFO - Data quality validation for EURUSD: Excellent (Score: 99.6)
2025-07-13 18:46:51,330 - BacktestEngine - INFO - Data quality for EURUSD: Excellent (Score: 99.6)
2025-07-13 18:46:51,331 - DataHandler - DEBUG - Created Backtrader feed for EURUSD
2025-07-13 18:46:51,331 - BacktestEngine - INFO - Successfully loaded 254 bars for EURUSD
2025-07-13 18:46:51,331 - BacktestEngine - DEBUG - About to add strategy
2025-07-13 18:46:51,331 - BacktestEngine - INFO - Adding strategy: ema_crossover
2025-07-13 18:46:51,331 - BacktestEngine - DEBUG - Adding strategy <class 'strategy.minimal_strategy.MinimalStrategy'> with params: {'ema_fast': 55, 'ema_slow': 89}
2025-07-13 18:46:51,331 - BacktestEngine - INFO - Strategy ema_crossover added successfully
2025-07-13 18:46:51,331 - BacktestEngine - DEBUG - Strategy added successfully
2025-07-13 18:46:51,331 - BacktestEngine - INFO - Starting backtest with 1 symbols
2025-07-13 18:46:51,331 - BacktestEngine - INFO - Initial portfolio value: $10,000.00
2025-07-13 18:46:51,331 - BacktestEngine - DEBUG - About to run cerebro.run()
2025-07-13 18:46:51,363 - BacktestEngine - ERROR - Error compiling results: 'NoneType' object has no attribute 'addindicator'
2025-07-13 18:46:51,363 - BacktestEngine - INFO - Backtest completed successfully
2025-07-13 18:46:51,363 - BacktestEngine - INFO - Final portfolio value: $9,884.44
2025-07-13 18:46:51,363 - __main__ - ERROR - Error running backtest: 'NoneType' object has no attribute 'addindicator'
2025-07-13 18:46:51,363 - __main__ - ERROR - Backtest failed
2025-07-13 18:49:07,968 - __main__ - INFO - Forex Backtesting System started
2025-07-13 18:49:07,970 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 18:49:07,970 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 18:49:07,970 - __main__ - INFO - Starting backtest execution
2025-07-13 18:49:07,970 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 18:49:07,971 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 18:49:07,971 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 18:49:07,971 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 18:49:07,971 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 18:49:07,971 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 18:49:07,971 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 18:49:07,971 - yfinance - DEBUG - Entering history()
2025-07-13 18:49:07,978 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 18:49:07,979 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 18:49:07,993 - yfinance - DEBUG -  Entering history()
2025-07-13 18:49:07,996 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 18:49:07,997 - yfinance - DEBUG -   Entering get()
2025-07-13 18:49:07,997 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 18:49:07,997 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 18:49:07,997 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 18:49:07,997 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 18:49:07,997 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 18:49:07,997 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 18:49:07,997 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 18:49:07,997 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 18:49:07,998 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 18:49:07,998 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 18:49:07,998 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 18:49:07,998 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 18:49:07,998 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 18:49:07,998 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 18:49:07,998 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 18:49:07,998 - yfinance - DEBUG - reusing cookie
2025-07-13 18:49:07,998 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 18:49:08,332 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 18:49:08,334 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 18:49:08,334 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 18:49:08,334 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 18:49:08,497 - yfinance - DEBUG - response code=200
2025-07-13 18:49:08,497 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 18:49:08,498 - yfinance - DEBUG -   Exiting get()
2025-07-13 18:49:08,512 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 18:49:08,516 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 18:49:08,524 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 18:49:08,529 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 18:49:08,529 - yfinance - DEBUG -  Exiting history()
2025-07-13 18:49:08,530 - yfinance - DEBUG - Exiting history()
2025-07-13 18:49:08,531 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 18:49:08,533 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 18:49:08,534 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 18:49:08,537 - DataHandler - INFO - Data quality validation for EURUSD: Excellent (Score: 99.6)
2025-07-13 18:49:08,537 - BacktestEngine - INFO - Data quality for EURUSD: Excellent (Score: 99.6)
2025-07-13 18:49:08,537 - DataHandler - DEBUG - Created Backtrader feed for EURUSD
2025-07-13 18:49:08,537 - BacktestEngine - INFO - Successfully loaded 254 bars for EURUSD
2025-07-13 18:49:08,537 - BacktestEngine - DEBUG - About to add strategy
2025-07-13 18:49:08,537 - BacktestEngine - INFO - Adding strategy: ema_crossover
2025-07-13 18:49:08,537 - BacktestEngine - DEBUG - Adding strategy <class 'strategy.minimal_strategy.MinimalStrategy'> with params: {'ema_fast': 55, 'ema_slow': 89}
2025-07-13 18:49:08,537 - BacktestEngine - INFO - Strategy ema_crossover added successfully
2025-07-13 18:49:08,537 - BacktestEngine - DEBUG - Strategy added successfully
2025-07-13 18:49:08,537 - BacktestEngine - INFO - Starting backtest with 1 symbols
2025-07-13 18:49:08,538 - BacktestEngine - INFO - Initial portfolio value: $10,000.00
2025-07-13 18:49:08,538 - BacktestEngine - DEBUG - About to run cerebro.run()
2025-07-13 18:49:08,571 - BacktestEngine - ERROR - Error compiling results: 'NoneType' object has no attribute 'addindicator'
2025-07-13 18:49:08,571 - BacktestEngine - INFO - Backtest completed successfully
2025-07-13 18:49:08,571 - BacktestEngine - INFO - Final portfolio value: $9,884.44
2025-07-13 18:49:08,571 - __main__ - ERROR - Error running backtest: 'NoneType' object has no attribute 'addindicator'
2025-07-13 18:49:08,572 - __main__ - ERROR - Backtest failed
2025-07-13 18:51:11,928 - __main__ - INFO - Forex Backtesting System started
2025-07-13 18:51:11,932 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 18:51:11,932 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 18:51:11,932 - __main__ - INFO - Starting backtest execution
2025-07-13 18:51:11,933 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 18:51:11,933 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 18:51:11,933 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 18:51:11,933 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 18:51:11,933 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 18:51:11,933 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 18:51:11,933 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 18:51:11,933 - yfinance - DEBUG - Entering history()
2025-07-13 18:51:11,940 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 18:51:11,940 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 18:51:11,956 - yfinance - DEBUG -  Entering history()
2025-07-13 18:51:11,959 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 18:51:11,959 - yfinance - DEBUG -   Entering get()
2025-07-13 18:51:11,959 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 18:51:11,960 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 18:51:11,960 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 18:51:11,960 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 18:51:11,960 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 18:51:11,960 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 18:51:11,960 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 18:51:11,960 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 18:51:11,960 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 18:51:11,961 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 18:51:11,961 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 18:51:11,961 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 18:51:11,961 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 18:51:11,961 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 18:51:11,961 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 18:51:11,961 - yfinance - DEBUG - reusing cookie
2025-07-13 18:51:11,961 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 18:51:12,455 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 18:51:12,456 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 18:51:12,456 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 18:51:12,456 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 18:51:12,615 - yfinance - DEBUG - response code=200
2025-07-13 18:51:12,615 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 18:51:12,615 - yfinance - DEBUG -   Exiting get()
2025-07-13 18:51:12,630 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 18:51:12,633 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 18:51:12,640 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 18:51:12,645 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 18:51:12,645 - yfinance - DEBUG -  Exiting history()
2025-07-13 18:51:12,645 - yfinance - DEBUG - Exiting history()
2025-07-13 18:51:12,646 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 18:51:12,649 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 18:51:12,649 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 18:51:12,652 - DataHandler - INFO - Data quality validation for EURUSD: Excellent (Score: 99.6)
2025-07-13 18:51:12,652 - BacktestEngine - INFO - Data quality for EURUSD: Excellent (Score: 99.6)
2025-07-13 18:51:12,652 - DataHandler - DEBUG - Created Backtrader feed for EURUSD
2025-07-13 18:51:12,652 - BacktestEngine - INFO - Successfully loaded 254 bars for EURUSD
2025-07-13 18:51:12,652 - BacktestEngine - DEBUG - About to add strategy
2025-07-13 18:51:12,652 - BacktestEngine - INFO - Adding strategy: ema_crossover
2025-07-13 18:51:12,652 - BacktestEngine - DEBUG - Adding strategy <class 'strategy.minimal_strategy.MinimalStrategy'> with params: {'ema_fast': 55, 'ema_slow': 89}
2025-07-13 18:51:12,652 - BacktestEngine - INFO - Strategy ema_crossover added successfully
2025-07-13 18:51:12,652 - BacktestEngine - DEBUG - Strategy added successfully
2025-07-13 18:51:12,652 - BacktestEngine - INFO - Starting backtest with 1 symbols
2025-07-13 18:51:12,652 - BacktestEngine - INFO - Initial portfolio value: $10,000.00
2025-07-13 18:51:12,652 - BacktestEngine - DEBUG - About to run cerebro.run()
2025-07-13 18:51:12,685 - BacktestEngine - ERROR - Error compiling results: 'NoneType' object has no attribute 'addindicator'
2025-07-13 18:51:12,685 - BacktestEngine - INFO - Backtest completed successfully
2025-07-13 18:51:12,685 - BacktestEngine - INFO - Final portfolio value: $9,884.44
2025-07-13 18:51:12,685 - __main__ - ERROR - Error running backtest: 'NoneType' object has no attribute 'addindicator'
2025-07-13 18:51:12,685 - __main__ - ERROR - Backtest failed
2025-07-13 18:51:53,577 - __main__ - INFO - Forex Backtesting System started
2025-07-13 18:51:53,578 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 18:51:53,578 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 18:51:53,578 - __main__ - INFO - Starting backtest execution
2025-07-13 18:51:53,578 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 18:51:53,578 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 18:51:53,578 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 18:51:53,578 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 18:51:53,578 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 18:51:53,578 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 18:51:53,578 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 18:51:53,579 - yfinance - DEBUG - Entering history()
2025-07-13 18:51:53,584 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 18:51:53,585 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 18:51:53,600 - yfinance - DEBUG -  Entering history()
2025-07-13 18:51:53,603 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 18:51:53,603 - yfinance - DEBUG -   Entering get()
2025-07-13 18:51:53,603 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 18:51:53,603 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 18:51:53,604 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 18:51:53,604 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 18:51:53,604 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 18:51:53,604 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 18:51:53,604 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 18:51:53,604 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 18:51:53,604 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 18:51:53,605 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 18:51:53,605 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 18:51:53,605 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 18:51:53,605 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 18:51:53,605 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 18:51:53,605 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 18:51:53,605 - yfinance - DEBUG - reusing cookie
2025-07-13 18:51:53,605 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 18:51:53,948 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 18:51:53,950 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 18:51:53,950 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 18:51:53,950 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 18:51:54,103 - yfinance - DEBUG - response code=200
2025-07-13 18:51:54,103 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 18:51:54,104 - yfinance - DEBUG -   Exiting get()
2025-07-13 18:51:54,120 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 18:51:54,124 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 18:51:54,131 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 18:51:54,137 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 18:51:54,137 - yfinance - DEBUG -  Exiting history()
2025-07-13 18:51:54,137 - yfinance - DEBUG - Exiting history()
2025-07-13 18:51:54,138 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 18:51:54,141 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 18:51:54,141 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 18:51:54,144 - DataHandler - INFO - Data quality validation for EURUSD: Excellent (Score: 99.6)
2025-07-13 18:51:54,144 - BacktestEngine - INFO - Data quality for EURUSD: Excellent (Score: 99.6)
2025-07-13 18:51:54,144 - DataHandler - DEBUG - Created Backtrader feed for EURUSD
2025-07-13 18:51:54,144 - BacktestEngine - INFO - Successfully loaded 254 bars for EURUSD
2025-07-13 18:51:54,144 - BacktestEngine - DEBUG - About to add strategy
2025-07-13 18:51:54,144 - BacktestEngine - INFO - Adding strategy: ema_crossover
2025-07-13 18:51:54,144 - BacktestEngine - DEBUG - Adding strategy <class 'strategy.minimal_strategy.MinimalStrategy'> with params: {'ema_fast': 55, 'ema_slow': 89}
2025-07-13 18:51:54,145 - BacktestEngine - INFO - Strategy ema_crossover added successfully
2025-07-13 18:51:54,145 - BacktestEngine - DEBUG - Strategy added successfully
2025-07-13 18:51:54,145 - BacktestEngine - INFO - Starting backtest with 1 symbols
2025-07-13 18:51:54,145 - BacktestEngine - INFO - Initial portfolio value: $10,000.00
2025-07-13 18:51:54,145 - BacktestEngine - DEBUG - About to run cerebro.run()
2025-07-13 18:51:54,177 - BacktestEngine - ERROR - Error compiling results: 'NoneType' object has no attribute 'addindicator'
2025-07-13 18:51:54,177 - BacktestEngine - INFO - Backtest completed successfully
2025-07-13 18:51:54,177 - BacktestEngine - INFO - Final portfolio value: $9,884.44
2025-07-13 18:51:54,178 - __main__ - ERROR - Error running backtest: 'NoneType' object has no attribute 'addindicator'
2025-07-13 18:51:54,178 - __main__ - ERROR - Backtest failed
2025-07-13 18:52:13,734 - __main__ - INFO - Forex Backtesting System started
2025-07-13 18:52:13,736 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 18:52:13,736 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 18:52:13,736 - __main__ - INFO - Starting backtest execution
2025-07-13 18:52:13,737 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 18:52:13,737 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 18:52:13,737 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 18:52:13,737 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 18:52:13,737 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 18:52:13,737 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 18:52:13,737 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 18:52:13,737 - yfinance - DEBUG - Entering history()
2025-07-13 18:52:13,742 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 18:52:13,743 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 18:52:13,758 - yfinance - DEBUG -  Entering history()
2025-07-13 18:52:13,760 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 18:52:13,761 - yfinance - DEBUG -   Entering get()
2025-07-13 18:52:13,761 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 18:52:13,761 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 18:52:13,761 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 18:52:13,761 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 18:52:13,761 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 18:52:13,761 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 18:52:13,761 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 18:52:13,761 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 18:52:13,762 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 18:52:13,763 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 18:52:13,763 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 18:52:13,763 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 18:52:13,763 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 18:52:13,763 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 18:52:13,763 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 18:52:13,763 - yfinance - DEBUG - reusing cookie
2025-07-13 18:52:13,763 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 18:52:14,329 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 18:52:14,332 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 18:52:14,332 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 18:52:14,332 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 18:52:14,475 - yfinance - DEBUG - response code=200
2025-07-13 18:52:14,475 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 18:52:14,475 - yfinance - DEBUG -   Exiting get()
2025-07-13 18:52:14,487 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 18:52:14,491 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 18:52:14,497 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 18:52:14,503 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 18:52:14,503 - yfinance - DEBUG -  Exiting history()
2025-07-13 18:52:14,503 - yfinance - DEBUG - Exiting history()
2025-07-13 18:52:14,504 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 18:52:14,506 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 18:52:14,506 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 18:52:14,509 - DataHandler - INFO - Data quality validation for EURUSD: Excellent (Score: 99.6)
2025-07-13 18:52:14,509 - BacktestEngine - INFO - Data quality for EURUSD: Excellent (Score: 99.6)
2025-07-13 18:52:14,509 - DataHandler - DEBUG - Created Backtrader feed for EURUSD
2025-07-13 18:52:14,509 - BacktestEngine - INFO - Successfully loaded 254 bars for EURUSD
2025-07-13 18:52:14,509 - BacktestEngine - DEBUG - About to add strategy
2025-07-13 18:52:14,509 - BacktestEngine - INFO - Adding strategy: ema_crossover
2025-07-13 18:52:14,509 - BacktestEngine - DEBUG - Adding strategy <class 'strategy.minimal_strategy.MinimalStrategy'> with params: {'ema_fast': 55, 'ema_slow': 89}
2025-07-13 18:52:14,509 - BacktestEngine - INFO - Strategy ema_crossover added successfully
2025-07-13 18:52:14,509 - BacktestEngine - DEBUG - Strategy added successfully
2025-07-13 18:52:14,509 - BacktestEngine - INFO - Starting backtest with 1 symbols
2025-07-13 18:52:14,509 - BacktestEngine - INFO - Initial portfolio value: $10,000.00
2025-07-13 18:52:14,509 - BacktestEngine - DEBUG - About to run cerebro.run()
2025-07-13 18:52:14,541 - BacktestEngine - ERROR - Error compiling results: 'NoneType' object has no attribute 'addindicator'
2025-07-13 18:52:14,541 - BacktestEngine - INFO - Backtest completed successfully
2025-07-13 18:52:14,541 - BacktestEngine - INFO - Final portfolio value: $9,884.44
2025-07-13 18:52:14,541 - __main__ - ERROR - Error running backtest: 'NoneType' object has no attribute 'addindicator'
2025-07-13 18:52:14,541 - __main__ - ERROR - Backtest failed
2025-07-13 18:52:39,348 - __main__ - INFO - Forex Backtesting System started
2025-07-13 18:52:39,349 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 18:52:39,349 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 18:52:39,349 - __main__ - INFO - Starting backtest execution
2025-07-13 18:52:39,349 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 18:52:39,349 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 18:52:39,349 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 18:52:39,349 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 18:52:39,349 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 18:52:39,349 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 18:52:39,349 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 18:52:39,349 - yfinance - DEBUG - Entering history()
2025-07-13 18:52:39,355 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 18:52:39,355 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 18:52:39,369 - yfinance - DEBUG -  Entering history()
2025-07-13 18:52:39,371 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 18:52:39,372 - yfinance - DEBUG -   Entering get()
2025-07-13 18:52:39,372 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 18:52:39,372 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 18:52:39,372 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 18:52:39,372 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 18:52:39,372 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 18:52:39,372 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 18:52:39,372 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 18:52:39,372 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 18:52:39,373 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 18:52:39,373 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 18:52:39,373 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 18:52:39,373 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 18:52:39,373 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 18:52:39,373 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 18:52:39,373 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 18:52:39,373 - yfinance - DEBUG - reusing cookie
2025-07-13 18:52:39,373 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 18:52:39,818 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 18:52:39,818 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 18:52:39,818 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 18:52:39,818 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 18:52:40,167 - yfinance - DEBUG - response code=200
2025-07-13 18:52:40,167 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 18:52:40,167 - yfinance - DEBUG -   Exiting get()
2025-07-13 18:52:40,176 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 18:52:40,179 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 18:52:40,182 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 18:52:40,185 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 18:52:40,186 - yfinance - DEBUG -  Exiting history()
2025-07-13 18:52:40,186 - yfinance - DEBUG - Exiting history()
2025-07-13 18:52:40,186 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 18:52:40,187 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 18:52:40,187 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 18:52:40,189 - DataHandler - INFO - Data quality validation for EURUSD: Excellent (Score: 99.6)
2025-07-13 18:52:40,189 - BacktestEngine - INFO - Data quality for EURUSD: Excellent (Score: 99.6)
2025-07-13 18:52:40,189 - DataHandler - DEBUG - Created Backtrader feed for EURUSD
2025-07-13 18:52:40,189 - BacktestEngine - INFO - Successfully loaded 254 bars for EURUSD
2025-07-13 18:52:40,189 - BacktestEngine - DEBUG - About to add strategy
2025-07-13 18:52:40,189 - BacktestEngine - INFO - Adding strategy: ema_crossover
2025-07-13 18:52:40,189 - BacktestEngine - DEBUG - Adding strategy <class 'strategy.minimal_strategy.MinimalStrategy'> with params: {'ema_fast': 55, 'ema_slow': 89}
2025-07-13 18:52:40,189 - BacktestEngine - INFO - Strategy ema_crossover added successfully
2025-07-13 18:52:40,189 - BacktestEngine - DEBUG - Strategy added successfully
2025-07-13 18:52:40,189 - BacktestEngine - INFO - Starting backtest with 1 symbols
2025-07-13 18:52:40,189 - BacktestEngine - INFO - Initial portfolio value: $10,000.00
2025-07-13 18:52:40,189 - BacktestEngine - DEBUG - About to run cerebro.run()
2025-07-13 18:52:40,217 - BacktestEngine - DEBUG - Creating basic results structure
2025-07-13 18:52:40,217 - BacktestEngine - DEBUG - Basic results structure created successfully
2025-07-13 18:52:40,217 - BacktestEngine - ERROR - Error compiling results: 'NoneType' object has no attribute 'addindicator'
2025-07-13 18:52:40,217 - BacktestEngine - INFO - Backtest completed successfully
2025-07-13 18:52:40,217 - BacktestEngine - INFO - Final portfolio value: $9,884.44
2025-07-13 18:52:40,217 - __main__ - ERROR - Error running backtest: 'NoneType' object has no attribute 'addindicator'
2025-07-13 18:52:40,217 - __main__ - ERROR - Backtest failed
2025-07-13 18:53:27,467 - __main__ - INFO - Forex Backtesting System started
2025-07-13 18:53:27,471 - __main__ - INFO - Strategy: ema_crossover
2025-07-13 18:53:27,471 - __main__ - INFO - Period: 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 18:53:27,471 - __main__ - INFO - Starting backtest execution
2025-07-13 18:53:27,472 - BacktestEngine - INFO - Backtesting engine initialized
2025-07-13 18:53:27,472 - BacktestEngine - INFO - Starting backtest execution
2025-07-13 18:53:27,472 - BacktestEngine - WARNING - CalmarRatio analyzer not available
2025-07-13 18:53:27,472 - BacktestEngine - DEBUG - Performance analyzers added to Cerebro
2025-07-13 18:53:27,472 - BacktestEngine - INFO - Cerebro configured with initial cash: $10,000.00
2025-07-13 18:53:27,472 - BacktestEngine - INFO - Loading data for EURUSD from 2024-01-01 00:00:00 to 2024-12-31 00:00:00
2025-07-13 18:53:27,472 - DataHandler - INFO - Fetching data for EURUSD (EURUSD=X) from 2024-01-01 00:00:00 to 2024-12-31 00:00:00 with D1 timeframe
2025-07-13 18:53:27,472 - yfinance - DEBUG - Entering history()
2025-07-13 18:53:27,477 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-13 18:53:27,477 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['EURUSD=X', 1, 0])
2025-07-13 18:53:27,492 - yfinance - DEBUG -  Entering history()
2025-07-13 18:53:27,494 - yfinance - DEBUG - EURUSD=X: Yahoo GET parameters: {'period1': '2024-01-01 00:00:00+00:00', 'period2': '2024-12-31 00:00:00+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-13 18:53:27,495 - yfinance - DEBUG -   Entering get()
2025-07-13 18:53:27,495 - yfinance - DEBUG -    Entering _make_request()
2025-07-13 18:53:27,495 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/EURUSD=X
2025-07-13 18:53:27,495 - yfinance - DEBUG - params=frozendict.frozendict({'period1': 1704067200, 'period2': 1735603200, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'})
2025-07-13 18:53:27,495 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-13 18:53:27,495 - yfinance - DEBUG - cookie_mode = 'basic'
2025-07-13 18:53:27,495 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-13 18:53:27,495 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-07-13 18:53:27,495 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-13 18:53:27,495 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-13 18:53:27,496 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-13 18:53:27,496 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-13 18:53:27,496 - yfinance - DEBUG - reusing persistent cookie
2025-07-13 18:53:27,496 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-07-13 18:53:27,496 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-07-13 18:53:27,496 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-07-13 18:53:27,496 - yfinance - DEBUG - reusing cookie
2025-07-13 18:53:27,496 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-07-13 18:53:27,821 - yfinance - DEBUG - crumb = '6/fRVkYpYes'
2025-07-13 18:53:27,821 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-07-13 18:53:27,822 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-13 18:53:27,822 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-13 18:53:27,961 - yfinance - DEBUG - response code=200
2025-07-13 18:53:27,962 - yfinance - DEBUG -    Exiting _make_request()
2025-07-13 18:53:27,963 - yfinance - DEBUG -   Exiting get()
2025-07-13 18:53:27,974 - yfinance - DEBUG - EURUSD=X: yfinance received OHLC data: 2024-01-01 00:00:00 -> 2024-12-30 00:00:00
2025-07-13 18:53:27,978 - yfinance - DEBUG - EURUSD=X: OHLC after cleaning: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 18:53:27,986 - yfinance - DEBUG - EURUSD=X: OHLC after combining events: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 18:53:27,991 - yfinance - DEBUG - EURUSD=X: yfinance returning OHLC: 2024-01-01 00:00:00+00:00 -> 2024-12-30 00:00:00+00:00
2025-07-13 18:53:27,992 - yfinance - DEBUG -  Exiting history()
2025-07-13 18:53:27,992 - yfinance - DEBUG - Exiting history()
2025-07-13 18:53:27,993 - DataHandler - WARNING - Found 7 bars with invalid OHLC relationships
2025-07-13 18:53:27,995 - DataHandler - INFO - Data cleaning completed for EURUSD: 254 valid bars
2025-07-13 18:53:27,995 - DataHandler - INFO - Successfully fetched 254 bars for EURUSD
2025-07-13 18:53:27,998 - DataHandler - INFO - Data quality validation for EURUSD: Excellent (Score: 99.6)
2025-07-13 18:53:27,998 - BacktestEngine - INFO - Data quality for EURUSD: Excellent (Score: 99.6)
2025-07-13 18:53:27,998 - DataHandler - DEBUG - Created Backtrader feed for EURUSD
2025-07-13 18:53:27,998 - BacktestEngine - INFO - Successfully loaded 254 bars for EURUSD
2025-07-13 18:53:27,998 - BacktestEngine - DEBUG - About to add strategy
2025-07-13 18:53:27,998 - BacktestEngine - INFO - Adding strategy: ema_crossover
2025-07-13 18:53:27,998 - BacktestEngine - DEBUG - Adding strategy <class 'strategy.minimal_strategy.MinimalStrategy'> with params: {'ema_fast': 55, 'ema_slow': 89}
2025-07-13 18:53:27,998 - BacktestEngine - INFO - Strategy ema_crossover added successfully
2025-07-13 18:53:27,998 - BacktestEngine - DEBUG - Strategy added successfully
2025-07-13 18:53:27,999 - BacktestEngine - INFO - Starting backtest with 1 symbols
2025-07-13 18:53:27,999 - BacktestEngine - INFO - Initial portfolio value: $10,000.00
2025-07-13 18:53:27,999 - BacktestEngine - DEBUG - About to run cerebro.run()
2025-07-13 18:53:28,031 - BacktestEngine - DEBUG - Creating basic results structure
2025-07-13 18:53:28,032 - BacktestEngine - DEBUG - Basic results structure created successfully
2025-07-13 18:53:28,032 - BacktestEngine - INFO - Backtest completed successfully
2025-07-13 18:53:28,032 - BacktestEngine - INFO - Final portfolio value: $9,884.44
2025-07-13 18:53:28,032 - BacktestEngine - INFO - Total return: -1.16%
2025-07-13 18:53:28,032 - __main__ - ERROR - Error running backtest: 'NoneType' object has no attribute 'addindicator'
2025-07-13 18:53:28,032 - __main__ - ERROR - Backtest failed
