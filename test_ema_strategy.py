"""
Test the EMA strategy directly.
"""

import backtrader as bt
import pandas as pd
import numpy as np
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)

from strategy.ema_crossover_strategy import EMACrossoverStrategy


def test_ema_strategy():
    """Test EMA strategy directly."""
    print("Creating test data...")
    
    # Create simple test data
    dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='D')
    np.random.seed(42)
    
    # Generate trending price data
    price = 1.0
    prices = []
    for i in range(len(dates)):
        # Add some trend
        trend = 0.0001 * i
        price += np.random.randn() * 0.01 + trend
        prices.append(price)
    
    data = pd.DataFrame({
        'open': prices,
        'high': [p * (1 + abs(np.random.randn() * 0.005)) for p in prices],
        'low': [p * (1 - abs(np.random.randn() * 0.005)) for p in prices],
        'close': prices,
        'volume': [1000] * len(prices)
    }, index=dates)
    
    print(f"Created {len(data)} bars of test data")
    
    # Create Cerebro
    cerebro = bt.Cerebro()
    
    # Add data
    data_feed = bt.feeds.PandasData(dataname=data)
    cerebro.adddata(data_feed)
    
    # Add strategy with parameters
    cerebro.addstrategy(
        EMACrossoverStrategy,
        ema_fast=20,
        ema_slow=50,
        atr_period=14,
        volatility_multiplier=2.0,
        max_positions=3,
        position_size_percent=0.1
    )
    
    # Set cash and commission
    cerebro.broker.setcash(10000)
    cerebro.broker.setcommission(commission=0.001)
    
    print("Running EMA strategy backtest...")
    
    try:
        # Run backtest
        results = cerebro.run()
        
        print("Backtest completed!")
        print(f"Final value: {cerebro.broker.getvalue():.2f}")
        
        if results:
            strategy = results[0]
            if hasattr(strategy, 'get_strategy_stats'):
                stats = strategy.get_strategy_stats()
                print(f"Total trades: {stats.get('total_trades', 0)}")
                print(f"Win rate: {stats.get('win_rate', 0):.1%}")
        
    except Exception as e:
        print(f"Error running backtest: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_ema_strategy()
