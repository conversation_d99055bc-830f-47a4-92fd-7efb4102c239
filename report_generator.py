"""
Report generator module for creating comprehensive HTML reports.
Uses Jinja2 templates to generate detailed backtesting reports with charts and analysis.
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from jinja2 import Environment, FileSystemLoader, Template
import pandas as pd

from config import get_config
from visualization import ChartVisualizer


class ReportGenerator:
    """Generates comprehensive HTML reports for backtesting results."""
    
    def __init__(self):
        """Initialize the report generator."""
        self.config = get_config()
        self.logger = logging.getLogger(self.__class__.__name__)
        self.visualizer = ChartVisualizer()
        
        # Setup Jinja2 environment
        template_dir = os.path.join(os.path.dirname(__file__), 'templates')
        self.jinja_env = Environment(
            loader=FileSystemLoader(template_dir),
            autoescape=True
        )
        
        self.logger.info("Report generator initialized")
    
    def generate_report(self, results: Dict[str, Any], data: pd.DataFrame = None, 
                       trades: List[Dict] = None, indicators: Dict[str, pd.Series] = None,
                       output_filename: str = None) -> str:
        """
        Generate a comprehensive HTML report.
        
        Args:
            results: Backtest results dictionary
            data: OHLCV data DataFrame
            trades: List of trade dictionaries
            indicators: Dictionary of indicator series
            output_filename: Optional custom output filename
        
        Returns:
            Path to generated report file
        """
        try:
            self.logger.info("Starting report generation")
            
            # Prepare report data
            report_data = self._prepare_report_data(results, trades)
            
            # Generate charts if requested
            chart_data = {}
            if self.config.get_include_charts() and data is not None:
                chart_data = self._generate_charts(data, trades or [], indicators or {})
            
            # Combine all data for template
            template_data = {
                **report_data,
                **chart_data,
                'include_charts': self.config.get_include_charts(),
                'generation_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # Generate HTML report
            report_html = self._render_template(template_data)
            
            # Save report to file
            if not output_filename:
                output_filename = self.config.get_report_filename()
            
            report_path = self._save_report(report_html, output_filename)
            
            self.logger.info(f"Report generated successfully: {report_path}")
            return report_path
            
        except Exception as e:
            self.logger.error(f"Error generating report: {e}")
            raise
    
    def _prepare_report_data(self, results: Dict[str, Any], 
                           trades: List[Dict] = None) -> Dict[str, Any]:
        """
        Prepare data for the report template.
        
        Args:
            results: Backtest results dictionary
            trades: List of trade dictionaries
        
        Returns:
            Dictionary with prepared template data
        """
        try:
            # Extract basic information
            backtest_info = results.get('backtest_info', {})
            performance_metrics = results.get('performance_metrics', {})
            
            # Prepare template data
            template_data = {
                'strategy_name': backtest_info.get('strategy', 'Unknown Strategy'),
                'start_date': backtest_info.get('start_date', '').strftime('%Y-%m-%d') if backtest_info.get('start_date') else 'N/A',
                'end_date': backtest_info.get('end_date', '').strftime('%Y-%m-%d') if backtest_info.get('end_date') else 'N/A',
                'currency_pairs': backtest_info.get('symbols', []),
                'performance_metrics': performance_metrics,
                'sharpe_ratio': results.get('sharpe_ratio', 0),
                'calmar_ratio': results.get('calmar_ratio', 0),
                'drawdown': results.get('drawdown', {}),
                'trade_analysis': results.get('trade_analysis', {}),
                'trades': trades or [],
                'strategy_params': self._get_strategy_parameters()
            }
            
            # Add additional calculated metrics
            template_data.update(self._calculate_additional_template_metrics(results, trades))
            
            return template_data
            
        except Exception as e:
            self.logger.error(f"Error preparing report data: {e}")
            return {}
    
    def _calculate_additional_template_metrics(self, results: Dict[str, Any], 
                                             trades: List[Dict] = None) -> Dict[str, Any]:
        """Calculate additional metrics for the template."""
        additional_metrics = {}
        
        try:
            if trades:
                # Calculate trade statistics
                profitable_trades = [t for t in trades if t.get('pnl', 0) > 0]
                losing_trades = [t for t in trades if t.get('pnl', 0) < 0]
                
                additional_metrics.update({
                    'total_trades_count': len(trades),
                    'profitable_trades_count': len(profitable_trades),
                    'losing_trades_count': len(losing_trades),
                    'avg_profit': sum(t.get('pnl', 0) for t in profitable_trades) / max(len(profitable_trades), 1),
                    'avg_loss': sum(t.get('pnl', 0) for t in losing_trades) / max(len(losing_trades), 1),
                })
                
                # Martingale statistics
                martingale_trades = [t for t in trades if t.get('martingale_level', 0) > 0]
                additional_metrics['martingale_frequency'] = len(martingale_trades) / max(len(trades), 1)
                
                # Trade duration analysis (if timestamps available)
                durations = []
                for trade in trades:
                    if trade.get('entry_time') and trade.get('exit_time'):
                        duration = trade['exit_time'] - trade['entry_time']
                        durations.append(duration.total_seconds() / 3600)  # Convert to hours
                
                if durations:
                    additional_metrics.update({
                        'avg_trade_duration_hours': sum(durations) / len(durations),
                        'min_trade_duration_hours': min(durations),
                        'max_trade_duration_hours': max(durations)
                    })
            
            # Risk metrics
            performance_metrics = results.get('performance_metrics', {})
            total_return = performance_metrics.get('total_return', 0)
            max_drawdown = results.get('drawdown', {}).get('max', {}).get('drawdown', 0)
            
            if max_drawdown > 0:
                additional_metrics['return_to_drawdown_ratio'] = total_return / (max_drawdown / 100)
            
        except Exception as e:
            self.logger.warning(f"Error calculating additional metrics: {e}")
        
        return additional_metrics
    
    def _get_strategy_parameters(self) -> Dict[str, Any]:
        """Get strategy parameters for the report."""
        try:
            strategy_name = self.config.get_strategy_name()
            
            params = {
                'Strategy': strategy_name,
                'Initial Cash': f"${self.config.get_initial_cash():,.2f}",
                'Commission': f"{self.config.get_commission():.4f}",
                'Timeframe': self.config.get_timeframe(),
                'Max Positions': self.config.get_max_positions(),
                'Position Size': f"{self.config.get_position_size_percent():.1%}",
                'Martingale Multiplier': self.config.get_martingale_multiplier(),
                'Max Martingale Levels': self.config.get_max_martingale_levels(),
                'Volatility Method': self.config.get_volatility_method(),
                'ATR Period': self.config.get_atr_period(),
                'Volatility Multiplier': self.config.get_volatility_multiplier(),
            }
            
            # Add strategy-specific parameters
            if strategy_name == 'ema_crossover':
                params.update({
                    'Fast EMA': self.config.get_ema_fast(),
                    'Slow EMA': self.config.get_ema_slow(),
                })
            
            return params
            
        except Exception as e:
            self.logger.error(f"Error getting strategy parameters: {e}")
            return {}
    
    def _generate_charts(self, data: pd.DataFrame, trades: List[Dict], 
                        indicators: Dict[str, pd.Series]) -> Dict[str, str]:
        """
        Generate charts for the report.
        
        Args:
            data: OHLCV data
            trades: List of trades
            indicators: Dictionary of indicators
        
        Returns:
            Dictionary with chart JSON data
        """
        chart_data = {}
        
        try:
            # Generate main chart
            main_chart = self.visualizer.create_main_chart(
                data, trades, indicators, 
                title=f"{self.config.get_strategy_name()} - {', '.join(self.config.get_currency_pairs())}"
            )
            chart_data['main_chart_json'] = main_chart.to_json()
            
            # Generate trade analysis chart
            if trades:
                trade_chart = self.visualizer.create_trade_analysis_chart(trades)
                chart_data['trade_chart_json'] = trade_chart.to_json()
            
            self.logger.info("Charts generated successfully")
            
        except Exception as e:
            self.logger.error(f"Error generating charts: {e}")
        
        return chart_data
    
    def _render_template(self, template_data: Dict[str, Any]) -> str:
        """
        Render the HTML template with data.
        
        Args:
            template_data: Data for template rendering
        
        Returns:
            Rendered HTML string
        """
        try:
            template = self.jinja_env.get_template('report_template.html')
            html_content = template.render(**template_data)
            
            self.logger.debug("Template rendered successfully")
            return html_content
            
        except Exception as e:
            self.logger.error(f"Error rendering template: {e}")
            raise
    
    def _save_report(self, html_content: str, filename: str) -> str:
        """
        Save the HTML report to file.
        
        Args:
            html_content: Rendered HTML content
            filename: Output filename
        
        Returns:
            Path to saved file
        """
        try:
            # Ensure filename has .html extension
            if not filename.endswith('.html'):
                filename += '.html'
            
            # Create reports directory if it doesn't exist
            reports_dir = 'reports'
            os.makedirs(reports_dir, exist_ok=True)
            
            # Full path for the report
            report_path = os.path.join(reports_dir, filename)
            
            # Write HTML content to file
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            self.logger.info(f"Report saved to {report_path}")
            return report_path
            
        except Exception as e:
            self.logger.error(f"Error saving report: {e}")
            raise
    
    def generate_summary_report(self, multiple_results: List[Dict[str, Any]], 
                              output_filename: str = "strategy_comparison.html") -> str:
        """
        Generate a summary report comparing multiple strategies or runs.
        
        Args:
            multiple_results: List of result dictionaries
            output_filename: Output filename
        
        Returns:
            Path to generated report
        """
        try:
            self.logger.info("Generating strategy comparison report")
            
            # Create comparison chart
            comparison_chart = self.visualizer.create_strategy_comparison_chart(multiple_results)
            
            # Prepare comparison data
            comparison_data = []
            for result in multiple_results:
                backtest_info = result.get('backtest_info', {})
                performance_metrics = result.get('performance_metrics', {})
                
                comparison_data.append({
                    'strategy': backtest_info.get('strategy', 'Unknown'),
                    'total_return': performance_metrics.get('total_return', 0),
                    'sharpe_ratio': result.get('sharpe_ratio', 0),
                    'max_drawdown': result.get('drawdown', {}).get('max', {}).get('drawdown', 0),
                    'total_trades': performance_metrics.get('total_trades', 0),
                    'win_rate': performance_metrics.get('win_rate', 0),
                })
            
            # Create simple comparison template
            comparison_html = self._create_comparison_html(comparison_data, comparison_chart)
            
            # Save report
            report_path = self._save_report(comparison_html, output_filename)
            
            self.logger.info(f"Comparison report generated: {report_path}")
            return report_path
            
        except Exception as e:
            self.logger.error(f"Error generating comparison report: {e}")
            raise
    
    def _create_comparison_html(self, comparison_data: List[Dict], 
                              comparison_chart) -> str:
        """Create HTML for strategy comparison report."""
        chart_json = comparison_chart.to_json()
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Strategy Comparison Report</title>
            <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .container {{ max-width: 1200px; margin: 0 auto; }}
                h1 {{ color: #2196F3; text-align: center; }}
                .chart-container {{ margin: 30px 0; }}
                table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                th, td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Strategy Comparison Report</h1>
                <div class="chart-container">
                    <div id="comparison-chart"></div>
                </div>
                <table>
                    <tr>
                        <th>Strategy</th>
                        <th>Total Return</th>
                        <th>Sharpe Ratio</th>
                        <th>Max Drawdown</th>
                        <th>Total Trades</th>
                        <th>Win Rate</th>
                    </tr>
        """
        
        for data in comparison_data:
            html += f"""
                    <tr>
                        <td>{data['strategy']}</td>
                        <td>{data['total_return']:.2%}</td>
                        <td>{data['sharpe_ratio']:.3f}</td>
                        <td>{data['max_drawdown']:.2f}%</td>
                        <td>{data['total_trades']}</td>
                        <td>{data['win_rate']:.1%}</td>
                    </tr>
            """
        
        html += f"""
                </table>
            </div>
            <script>
                var chartData = {chart_json};
                Plotly.newPlot('comparison-chart', chartData.data, chartData.layout, {{responsive: true}});
            </script>
        </body>
        </html>
        """
        
        return html
    
    def export_results_to_csv(self, results: Dict[str, Any], trades: List[Dict] = None,
                             output_filename: str = "backtest_results.csv") -> str:
        """
        Export backtest results to CSV format.
        
        Args:
            results: Backtest results dictionary
            trades: List of trade dictionaries
            output_filename: Output CSV filename
        
        Returns:
            Path to exported CSV file
        """
        try:
            if not trades:
                self.logger.warning("No trades data available for CSV export")
                return ""
            
            # Convert trades to DataFrame
            trades_df = pd.DataFrame(trades)
            
            # Create exports directory
            exports_dir = 'exports'
            os.makedirs(exports_dir, exist_ok=True)
            
            # Save to CSV
            csv_path = os.path.join(exports_dir, output_filename)
            trades_df.to_csv(csv_path, index=False)
            
            self.logger.info(f"Results exported to CSV: {csv_path}")
            return csv_path
            
        except Exception as e:
            self.logger.error(f"Error exporting to CSV: {e}")
            return ""
