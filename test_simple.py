"""
Simple test to debug the backtesting system.
"""

import backtrader as bt
import pandas as pd
import numpy as np
from datetime import datetime, timedelta


class SimpleTestStrategy(bt.Strategy):
    """Simple test strategy."""
    
    def __init__(self):
        print("SimpleTestStrategy.__init__ called")
        print(f"self.data: {self.data}")
        print(f"self.datas: {self.datas}")
        
        # Simple moving average
        self.sma = bt.indicators.SimpleMovingAverage(self.data.close, period=20)
        print("SMA indicator created successfully")
    
    def next(self):
        if len(self.data) < 20:
            return
        
        if not self.position:
            if self.data.close[0] > self.sma[0]:
                self.buy()
        else:
            if self.data.close[0] < self.sma[0]:
                self.sell()


def test_simple_backtest():
    """Test simple backtest."""
    print("Creating test data...")
    
    # Create simple test data
    dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='D')
    np.random.seed(42)
    
    # Generate random walk price data
    price = 1.0
    prices = []
    for _ in range(len(dates)):
        price += np.random.randn() * 0.01
        prices.append(price)
    
    data = pd.DataFrame({
        'open': prices,
        'high': [p * (1 + abs(np.random.randn() * 0.005)) for p in prices],
        'low': [p * (1 - abs(np.random.randn() * 0.005)) for p in prices],
        'close': prices,
        'volume': [1000] * len(prices)
    }, index=dates)
    
    print(f"Created {len(data)} bars of test data")
    
    # Create Cerebro
    cerebro = bt.Cerebro()
    
    # Add data
    data_feed = bt.feeds.PandasData(dataname=data)
    cerebro.adddata(data_feed)
    
    # Add strategy
    cerebro.addstrategy(SimpleTestStrategy)
    
    # Set cash
    cerebro.broker.setcash(10000)
    
    print("Running backtest...")
    
    # Run backtest
    results = cerebro.run()
    
    print("Backtest completed!")
    print(f"Final value: {cerebro.broker.getvalue():.2f}")


if __name__ == "__main__":
    test_simple_backtest()
