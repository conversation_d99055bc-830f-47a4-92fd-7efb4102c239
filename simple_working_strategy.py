"""
Simple working strategy to test the system.
"""

import backtrader as bt
import logging


class SimpleWorkingStrategy(bt.Strategy):
    """Simple strategy that works with Backtrader."""
    
    params = (
        ('ema_fast', 20),
        ('ema_slow', 50),
    )
    
    def __init__(self):
        """Initialize the strategy."""
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Create indicators - Backtrader will handle data attachment
        self.ema_fast = bt.indicators.EMA(period=self.params.ema_fast)
        self.ema_slow = bt.indicators.EMA(period=self.params.ema_slow)
        
        # Crossover signal
        self.crossover = bt.indicators.CrossOver(self.ema_fast, self.ema_slow)
        
        self.logger.info("Simple strategy initialized")
    
    def next(self):
        """Strategy logic."""
        # Only trade if we have enough data
        if len(self.data) < self.params.ema_slow:
            return
        
        # Entry logic
        if not self.position:
            if self.crossover > 0:  # Fast EMA crosses above slow EMA
                self.buy()
                self.logger.info(f"Buy signal at {self.data.close[0]:.5f}")
        else:
            if self.crossover < 0:  # Fast EMA crosses below slow EMA
                self.sell()
                self.logger.info(f"Sell signal at {self.data.close[0]:.5f}")


def test_simple_strategy():
    """Test the simple strategy with the main system."""
    from config import get_config, setup_logging
    from data_handler import DataHandler
    import backtrader as bt
    
    # Setup
    config = get_config()
    setup_logging(config)
    
    # Get data
    data_handler = DataHandler()
    data = data_handler.fetch_data('EURUSD', config.get_start_date(), config.get_end_date(), 'D1')
    
    if data is None:
        print("Failed to get data")
        return
    
    # Create Cerebro
    cerebro = bt.Cerebro()
    
    # Add data
    data_feed = bt.feeds.PandasData(dataname=data)
    cerebro.adddata(data_feed)
    
    # Add strategy
    cerebro.addstrategy(SimpleWorkingStrategy, ema_fast=10, ema_slow=20)
    
    # Set cash
    cerebro.broker.setcash(10000)
    
    print(f"Starting value: {cerebro.broker.getvalue():.2f}")
    
    # Run
    results = cerebro.run()
    
    print(f"Final value: {cerebro.broker.getvalue():.2f}")
    print("Test completed successfully!")


if __name__ == "__main__":
    test_simple_strategy()
