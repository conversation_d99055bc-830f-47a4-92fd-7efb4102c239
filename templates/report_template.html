<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forex Backtesting Report - {{ strategy_name }}</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2196F3;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2196F3;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            font-size: 1.1em;
            margin: 10px 0;
        }
        .section {
            margin: 40px 0;
        }
        .section h2 {
            color: #2196F3;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .metric-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #2196F3;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .metric-card h3 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .metric-card .value {
            font-size: 1.8em;
            font-weight: bold;
            color: #2196F3;
        }
        .metric-card .positive {
            color: #4caf50;
        }
        .metric-card .negative {
            color: #f44336;
        }
        .chart-container {
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .trade-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .trade-table th {
            background: #2196F3;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }
        .trade-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #e0e0e0;
        }
        .trade-table tr:hover {
            background-color: #f8f9fa;
        }
        .profit {
            color: #4caf50;
            font-weight: bold;
        }
        .loss {
            color: #f44336;
            font-weight: bold;
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196F3;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .info-box h3 {
            color: #1976d2;
            margin-top: 0;
        }
        .warning-box {
            background: #fff3e0;
            border: 1px solid #ff9800;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .warning-box h3 {
            color: #f57c00;
            margin-top: 0;
        }
        .footer {
            text-align: center;
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            color: #666;
        }
        .timestamp {
            font-size: 0.9em;
            color: #999;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>Forex Backtesting Report</h1>
            <p><strong>Strategy:</strong> {{ strategy_name }}</p>
            <p><strong>Period:</strong> {{ start_date }} to {{ end_date }}</p>
            <p><strong>Currency Pairs:</strong> {{ currency_pairs | join(', ') }}</p>
            <p class="timestamp">Generated on {{ generation_time }}</p>
        </div>

        <!-- Executive Summary -->
        <div class="section">
            <h2>Executive Summary</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <h3>Total Return</h3>
                    <div class="value {{ 'positive' if performance_metrics.total_return > 0 else 'negative' }}">
                        {{ "%.2f%%" | format(performance_metrics.total_return * 100) }}
                    </div>
                </div>
                <div class="metric-card">
                    <h3>Total P&L</h3>
                    <div class="value {{ 'positive' if performance_metrics.total_pnl > 0 else 'negative' }}">
                        ${{ "%.2f" | format(performance_metrics.total_pnl) }}
                    </div>
                </div>
                <div class="metric-card">
                    <h3>Sharpe Ratio</h3>
                    <div class="value">{{ "%.3f" | format(sharpe_ratio or 0) }}</div>
                </div>
                <div class="metric-card">
                    <h3>Max Drawdown</h3>
                    <div class="value negative">{{ "%.2f%%" | format((drawdown.max.drawdown or 0)) }}</div>
                </div>
                <div class="metric-card">
                    <h3>Win Rate</h3>
                    <div class="value">{{ "%.1f%%" | format((performance_metrics.win_rate or 0) * 100) }}</div>
                </div>
                <div class="metric-card">
                    <h3>Total Trades</h3>
                    <div class="value">{{ performance_metrics.total_trades or 0 }}</div>
                </div>
            </div>
        </div>

        <!-- Main Chart -->
        {% if include_charts %}
        <div class="section">
            <h2>Price Chart & Indicators</h2>
            <div class="chart-container">
                <div id="main-chart"></div>
            </div>
        </div>
        {% endif %}

        <!-- Performance Metrics -->
        <div class="section">
            <h2>Detailed Performance Metrics</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <h3>Winning Trades</h3>
                    <div class="value positive">{{ performance_metrics.winning_trades or 0 }}</div>
                </div>
                <div class="metric-card">
                    <h3>Losing Trades</h3>
                    <div class="value negative">{{ performance_metrics.losing_trades or 0 }}</div>
                </div>
                <div class="metric-card">
                    <h3>Average Win</h3>
                    <div class="value positive">${{ "%.2f" | format(performance_metrics.avg_win or 0) }}</div>
                </div>
                <div class="metric-card">
                    <h3>Average Loss</h3>
                    <div class="value negative">${{ "%.2f" | format(performance_metrics.avg_loss or 0) }}</div>
                </div>
                <div class="metric-card">
                    <h3>Profit Factor</h3>
                    <div class="value">{{ "%.2f" | format(performance_metrics.profit_factor or 0) }}</div>
                </div>
                <div class="metric-card">
                    <h3>Calmar Ratio</h3>
                    <div class="value">{{ "%.3f" | format(calmar_ratio or 0) }}</div>
                </div>
            </div>
        </div>

        <!-- Risk Analysis -->
        <div class="section">
            <h2>Risk Analysis</h2>
            {% if drawdown.max.drawdown and drawdown.max.drawdown > 15 %}
            <div class="warning-box">
                <h3>⚠️ High Drawdown Warning</h3>
                <p>The maximum drawdown of {{ "%.2f%%" | format(drawdown.max.drawdown) }} exceeds 15%. Consider reducing position sizes or implementing additional risk management measures.</p>
            </div>
            {% endif %}
            
            <div class="info-box">
                <h3>Drawdown Statistics</h3>
                <p><strong>Maximum Drawdown:</strong> {{ "%.2f%%" | format(drawdown.max.drawdown or 0) }}</p>
                <p><strong>Longest Drawdown Period:</strong> {{ drawdown.max.len or 0 }} periods</p>
                <p><strong>Average Drawdown:</strong> {{ "%.2f%%" | format(drawdown.avg or 0) }}</p>
            </div>
        </div>

        <!-- Trade Analysis -->
        {% if trades %}
        <div class="section">
            <h2>Trade Analysis</h2>
            {% if include_charts %}
            <div class="chart-container">
                <div id="trade-analysis-chart"></div>
            </div>
            {% endif %}
            
            <h3>Recent Trades</h3>
            <table class="trade-table">
                <thead>
                    <tr>
                        <th>Entry Time</th>
                        <th>Signal</th>
                        <th>Entry Price</th>
                        <th>Exit Price</th>
                        <th>Size</th>
                        <th>P&L</th>
                        <th>Martingale Level</th>
                    </tr>
                </thead>
                <tbody>
                    {% for trade in trades[-20:] %}
                    <tr>
                        <td>{{ trade.entry_time.strftime('%Y-%m-%d %H:%M') if trade.entry_time else 'N/A' }}</td>
                        <td>{{ trade.signal }}</td>
                        <td>{{ "%.5f" | format(trade.entry_price) }}</td>
                        <td>{{ "%.5f" | format(trade.exit_price) if trade.exit_price else 'Open' }}</td>
                        <td>{{ "%.2f" | format(trade.size) }}</td>
                        <td class="{{ 'profit' if (trade.pnl or 0) > 0 else 'loss' }}">
                            ${{ "%.2f" | format(trade.pnl or 0) }}
                        </td>
                        <td>{{ trade.martingale_level or 0 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}

        <!-- Strategy Configuration -->
        <div class="section">
            <h2>Strategy Configuration</h2>
            <div class="info-box">
                <h3>Parameters Used</h3>
                {% for key, value in strategy_params.items() %}
                <p><strong>{{ key.replace('_', ' ').title() }}:</strong> {{ value }}</p>
                {% endfor %}
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>Report generated by Forex Backtesting System</p>
            <p class="timestamp">{{ generation_time }}</p>
        </div>
    </div>

    <!-- Chart Scripts -->
    {% if include_charts %}
    <script>
        // Main chart
        {% if main_chart_json %}
        var mainChartData = {{ main_chart_json | safe }};
        Plotly.newPlot('main-chart', mainChartData.data, mainChartData.layout, {responsive: true});
        {% endif %}

        // Trade analysis chart
        {% if trade_chart_json %}
        var tradeChartData = {{ trade_chart_json | safe }};
        Plotly.newPlot('trade-analysis-chart', tradeChartData.data, tradeChartData.layout, {responsive: true});
        {% endif %}
    </script>
    {% endif %}
</body>
</html>
