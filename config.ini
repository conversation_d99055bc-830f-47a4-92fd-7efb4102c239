[STRATEGY]
strategy_name = ema_crossover
ema_fast = 55
ema_slow = 89
atr_period = 14
volatility_multiplier = 2.0
max_positions = 5
volatility_method = ATR

[BACKTEST]
initial_cash = 10000
commission = 0.0001
slippage = 0.0001
timeframe = M5
start_date = 2023-01-01
end_date = 2024-01-01

[RISK_MANAGEMENT]
max_drawdown = 0.20
position_size_percent = 0.02
martingale_multiplier = 2.0
max_martingale_levels = 5

[DATA]
currency_pairs = EURUSD,GBPUSD,USDJPY,AUDNZD
data_source = yfinance
validate_data = true

[REPORTING]
generate_html_report = true
include_charts = true
chart_theme = plotly_white
report_filename = backtest_report.html

[LOGGING]
log_level = INFO
log_file = backtest.log
console_output = true
