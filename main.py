"""
Main execution script for the Forex backtesting system.
Single entry point with strategy factory integration and configuration management.
"""

import argparse
import logging
import sys
from datetime import datetime
from typing import Optional, Dict, Any

from config import get_config, setup_logging
from backtest_engine import BacktestEngine
from report_generator import ReportGenerator
from strategy import print_available_strategies, list_strategies
from data_handler import <PERSON>Handler


def setup_argument_parser() -> argparse.ArgumentParser:
    """Setup command line argument parser."""
    parser = argparse.ArgumentParser(
        description="Forex Backtesting System - Professional-grade backtesting with modular strategies",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                           # Run with default configuration
  python main.py --strategy ema_crossover  # Run specific strategy
  python main.py --symbol EURUSD          # Test single currency pair
  python main.py --list-strategies        # Show available strategies
  python main.py --optimize               # Run parameter optimization
        """
    )

    parser.add_argument(
        '--strategy', '-s',
        type=str,
        help='Strategy to use (overrides config file)'
    )

    parser.add_argument(
        '--symbol',
        type=str,
        help='Single currency pair to test (e.g., EURUSD)'
    )

    parser.add_argument(
        '--start-date',
        type=str,
        help='Start date for backtest (YYYY-MM-DD)'
    )

    parser.add_argument(
        '--end-date',
        type=str,
        help='End date for backtest (YYYY-MM-DD)'
    )

    parser.add_argument(
        '--initial-cash',
        type=float,
        help='Initial cash amount'
    )

    parser.add_argument(
        '--list-strategies',
        action='store_true',
        help='List all available strategies and exit'
    )

    parser.add_argument(
        '--no-report',
        action='store_true',
        help='Skip HTML report generation'
    )

    parser.add_argument(
        '--output-dir',
        type=str,
        default='.',
        help='Output directory for reports and exports'
    )

    parser.add_argument(
        '--optimize',
        action='store_true',
        help='Run parameter optimization (not implemented yet)'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )

    parser.add_argument(
        '--config',
        type=str,
        default='config.ini',
        help='Configuration file path'
    )

    return parser


def validate_arguments(args: argparse.Namespace) -> bool:
    """
    Validate command line arguments.

    Args:
        args: Parsed arguments

    Returns:
        True if valid, False otherwise
    """
    # Validate strategy if provided
    if args.strategy:
        available_strategies = list_strategies()
        if args.strategy not in available_strategies:
            print(f"Error: Unknown strategy '{args.strategy}'")
            print(f"Available strategies: {', '.join(available_strategies)}")
            return False

    # Validate dates if provided
    if args.start_date:
        try:
            datetime.strptime(args.start_date, '%Y-%m-%d')
        except ValueError:
            print(f"Error: Invalid start date format '{args.start_date}'. Use YYYY-MM-DD")
            return False

    if args.end_date:
        try:
            datetime.strptime(args.end_date, '%Y-%m-%d')
        except ValueError:
            print(f"Error: Invalid end date format '{args.end_date}'. Use YYYY-MM-DD")
            return False

    # Validate initial cash
    if args.initial_cash is not None and args.initial_cash <= 0:
        print(f"Error: Initial cash must be positive, got {args.initial_cash}")
        return False

    return True


def update_config_from_args(args: argparse.Namespace) -> None:
    """
    Update configuration based on command line arguments.

    Args:
        args: Parsed arguments
    """
    config = get_config()

    # Update strategy
    if args.strategy:
        config.update_config('STRATEGY', 'strategy_name', args.strategy)

    # Update dates
    if args.start_date:
        config.update_config('BACKTEST', 'start_date', args.start_date)

    if args.end_date:
        config.update_config('BACKTEST', 'end_date', args.end_date)

    # Update initial cash
    if args.initial_cash:
        config.update_config('BACKTEST', 'initial_cash', str(args.initial_cash))

    # Update logging level
    if args.verbose:
        config.update_config('LOGGING', 'log_level', 'DEBUG')


def run_backtest(args: argparse.Namespace) -> Optional[Dict[str, Any]]:
    """
    Run the backtest with given arguments.

    Args:
        args: Parsed arguments

    Returns:
        Backtest results or None if failed
    """
    try:
        logger = logging.getLogger(__name__)
        logger.info("Starting backtest execution")

        # Initialize backtest engine
        engine = BacktestEngine()

        # Run backtest
        symbol = args.symbol if args.symbol else None
        results = engine.run_backtest(symbol)

        if results is None:
            logger.error("Backtest execution failed")
            return None

        # Get additional data for reporting
        trades = engine.get_trade_list()

        logger.info("Backtest completed successfully")
        return {
            'results': results,
            'trades': trades,
            'engine': engine
        }

    except Exception as e:
        logging.getLogger(__name__).error(f"Error running backtest: {e}")
        return None


def generate_report(backtest_data: Dict[str, Any], args: argparse.Namespace) -> Optional[str]:
    """
    Generate HTML report from backtest results.

    Args:
        backtest_data: Backtest results and data
        args: Command line arguments

    Returns:
        Path to generated report or None if failed
    """
    if args.no_report:
        return None

    try:
        logger = logging.getLogger(__name__)
        logger.info("Generating HTML report")

        # Initialize report generator
        report_gen = ReportGenerator()

        # Extract data
        results = backtest_data['results']
        trades = backtest_data['trades']
        engine = backtest_data['engine']

        # Get data and indicators for charts (this would need to be implemented)
        # For now, we'll generate the report without detailed chart data

        # Generate report
        report_path = report_gen.generate_report(
            results=results,
            trades=trades
        )

        logger.info(f"Report generated: {report_path}")
        return report_path

    except Exception as e:
        logging.getLogger(__name__).error(f"Error generating report: {e}")
        return None


def print_results_summary(results: Dict[str, Any]) -> None:
    """
    Print a summary of backtest results to console.

    Args:
        results: Backtest results dictionary
    """
    try:
        print("\n" + "="*60)
        print("BACKTEST RESULTS SUMMARY")
        print("="*60)

        # Basic info
        backtest_info = results.get('backtest_info', {})
        print(f"Strategy: {backtest_info.get('strategy', 'Unknown')}")
        print(f"Period: {backtest_info.get('start_date', 'N/A')} to {backtest_info.get('end_date', 'N/A')}")
        print(f"Symbols: {', '.join(backtest_info.get('symbols', []))}")

        # Performance metrics
        performance = results.get('performance_metrics', {})
        print(f"\nInitial Value: ${backtest_info.get('initial_cash', 0):,.2f}")
        print(f"Final Value: ${backtest_info.get('final_value', 0):,.2f}")
        print(f"Total Return: {performance.get('total_return', 0):.2%}")
        print(f"Total P&L: ${performance.get('total_pnl', 0):,.2f}")

        # Risk metrics
        sharpe = results.get('sharpe_ratio', 0)
        drawdown = results.get('drawdown', {}).get('max', {}).get('drawdown', 0)
        print(f"Sharpe Ratio: {sharpe:.3f}")
        print(f"Max Drawdown: {drawdown:.2f}%")

        # Trade statistics
        total_trades = performance.get('total_trades', 0)
        win_rate = performance.get('win_rate', 0)
        print(f"\nTotal Trades: {total_trades}")
        print(f"Win Rate: {win_rate:.1%}")
        print(f"Winning Trades: {performance.get('winning_trades', 0)}")
        print(f"Losing Trades: {performance.get('losing_trades', 0)}")

        if performance.get('avg_win') and performance.get('avg_loss'):
            print(f"Average Win: ${performance.get('avg_win', 0):.2f}")
            print(f"Average Loss: ${abs(performance.get('avg_loss', 0)):.2f}")
            print(f"Win/Loss Ratio: {performance.get('win_loss_ratio', 0):.2f}")

        print("="*60)

    except Exception as e:
        logging.getLogger(__name__).error(f"Error printing results summary: {e}")


def main():
    """Main entry point for the backtesting system."""
    # Parse command line arguments
    parser = setup_argument_parser()
    args = parser.parse_args()

    # Handle list strategies command
    if args.list_strategies:
        print_available_strategies()
        return 0

    # Validate arguments
    if not validate_arguments(args):
        return 1

    try:
        # Initialize configuration
        config = get_config()

        # Update config from command line arguments
        update_config_from_args(args)

        # Setup logging
        setup_logging(config)
        logger = logging.getLogger(__name__)

        logger.info("Forex Backtesting System started")
        logger.info(f"Strategy: {config.get_strategy_name()}")
        logger.info(f"Period: {config.get_start_date()} to {config.get_end_date()}")

        # Run backtest
        backtest_data = run_backtest(args)

        if backtest_data is None:
            logger.error("Backtest failed")
            return 1

        # Print results summary
        print_results_summary(backtest_data['results'])

        # Generate report
        report_path = generate_report(backtest_data, args)

        if report_path:
            print(f"\nDetailed report generated: {report_path}")

        logger.info("Backtesting system completed successfully")
        return 0

    except KeyboardInterrupt:
        print("\nBacktest interrupted by user")
        return 1

    except Exception as e:
        logging.getLogger(__name__).error(f"Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
