"""
Base strategy class that all trading strategies inherit from.
Provides common functionality for position management, martingale scaling, and risk management.
"""

import backtrader as bt
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import numpy as np
from config import get_config


class BaseStrategy(bt.Strategy):
    """
    Base class that all strategies inherit from.
    Provides common functionality for position management, martingale scaling, and indicators.
    """
    
    # Default parameters that can be overridden by child strategies
    params = (
        ('atr_period', 14),
        ('volatility_multiplier', 2.0),
        ('max_positions', 5),
        ('position_size_percent', 0.02),
        ('martingale_multiplier', 2.0),
        ('max_martingale_levels', 5),
        ('max_drawdown', 0.20),
        ('volatility_method', 'ATR'),
    )
    
    def __init__(self):
        """Initialize the base strategy."""
        self.config = get_config()
        self.logger = logging.getLogger(self.__class__.__name__)

        # Position tracking
        self.positions_info = {}  # Track position information
        self.martingale_levels = {}  # Track martingale levels per position
        self.entry_prices = {}  # Track entry prices
        self.position_sizes = {}  # Track position sizes

        # Performance tracking
        self.trade_count = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.total_pnl = 0.0
        self.max_drawdown_reached = 0.0

        # Setup indicators directly in __init__ - Backtrader will handle data attachment
        try:
            self.setup_common_indicators()
            self.setup_strategy_indicators()
            self.setup_signals()
        except Exception as e:
            self.logger.error(f"Error setting up indicators: {e}")
            # Continue without indicators for now

        self.logger.info(f"Initialized {self.__class__.__name__} strategy")
    
    def setup_common_indicators(self):
        """Setup common indicators used by all strategies."""
        try:
            # Average True Range for volatility measurement
            self.atr = bt.indicators.AverageTrueRange(period=self.params.atr_period)

            # Bollinger Bands for alternative volatility measurement
            self.bollinger = bt.indicators.BollingerBands(period=20, devfactor=2.0)

            # Volume Weighted Average Price (if available)
            try:
                self.vwap = bt.indicators.VWAP()
            except AttributeError:
                self.logger.warning("VWAP indicator not available")
                self.vwap = None

            # Standard deviation for volatility
            self.stddev = bt.indicators.StandardDeviation(period=self.params.atr_period)

            self.logger.debug("Common indicators initialized")

        except Exception as e:
            self.logger.error(f"Error setting up common indicators: {e}")
            # Don't raise the exception, just log it and continue
            self.logger.warning("Continuing without some indicators")
    
    def setup_strategy_indicators(self):
        """Override this in child strategies to setup strategy-specific indicators."""
        pass
    
    def setup_signals(self):
        """Override this in child strategies to setup entry/exit signals."""
        pass
    
    def next(self):
        """Main strategy logic executed on each bar."""
        try:
            # Check for maximum drawdown protection
            if self._check_max_drawdown():
                self.logger.warning("Maximum drawdown reached, stopping strategy")
                return
            
            # Get entry signal
            signal = self.get_entry_signal()
            
            if signal:
                self._handle_entry_signal(signal)
            
            # Manage existing positions
            self._manage_positions()
            
            # Check martingale conditions
            self._check_martingale_conditions()
            
        except Exception as e:
            self.logger.error(f"Error in strategy execution: {e}")
            import traceback
            self.logger.debug(f"Full traceback: {traceback.format_exc()}")
    
    def get_entry_signal(self) -> Optional[str]:
        """
        Override this in child strategies to implement entry logic.
        
        Returns:
            'LONG', 'SHORT', or None
        """
        raise NotImplementedError("Child strategies must implement get_entry_signal()")
    
    def _handle_entry_signal(self, signal: str):
        """Handle entry signal and create position."""
        if not self._can_enter_position():
            return
        
        position_size = self._calculate_position_size()
        
        if signal == 'LONG':
            order = self.buy(size=position_size)
            self.logger.info(f"Long entry signal: size={position_size}, price={self.data.close[0]}")
        elif signal == 'SHORT':
            order = self.sell(size=position_size)
            self.logger.info(f"Short entry signal: size={position_size}, price={self.data.close[0]}")
        
        if order:
            # Track position information
            position_id = len(self.positions_info)
            entry_price = float(self.data.close[0])
            self.positions_info[position_id] = {
                'order': order,
                'signal': signal,
                'entry_price': entry_price,
                'size': position_size,
                'timestamp': self.data.datetime.datetime(0),
                'martingale_level': 0
            }
            self.martingale_levels[position_id] = 0
            self.entry_prices[position_id] = entry_price
            self.position_sizes[position_id] = position_size
    
    def _can_enter_position(self) -> bool:
        """Check if we can enter a new position."""
        # Check maximum positions limit
        active_positions = len([p for p in self.positions_info.values() 
                              if p.get('status', 'active') == 'active'])
        
        if active_positions >= self.params.max_positions:
            self.logger.debug("Maximum positions reached")
            return False
        
        # Check available cash
        available_cash = self.broker.get_cash()
        required_cash = self._calculate_position_size() * self.data.close[0]
        
        if available_cash < required_cash:
            self.logger.debug("Insufficient cash for new position")
            return False
        
        return True
    
    def _calculate_position_size(self, martingale_level: int = 0) -> float:
        """
        Calculate position size based on account balance and martingale level.
        
        Args:
            martingale_level: Current martingale level (0 = initial position)
        
        Returns:
            Position size
        """
        account_value = self.broker.get_value()
        base_size = account_value * self.params.position_size_percent
        
        # Apply martingale multiplier
        if martingale_level > 0:
            multiplier = self.params.martingale_multiplier ** martingale_level
            base_size *= multiplier
        
        # Convert to number of units (for forex, this is typically lot size)
        current_price = float(self.data.close[0])
        position_size = base_size / current_price

        return max(position_size, 0.01)  # Minimum position size
    
    def _manage_positions(self):
        """Manage existing positions - check for take profit conditions."""
        for position_id, position_info in self.positions_info.items():
            if position_info.get('status', 'active') != 'active':
                continue
            
            current_price = float(self.data.close[0])
            entry_price = float(position_info['entry_price'])
            signal = position_info['signal']

            # Calculate take profit target based on volatility
            take_profit_distance = float(self._calculate_volatility_distance())

            should_close = False

            if signal == 'LONG':
                take_profit_price = entry_price + take_profit_distance
                if current_price >= take_profit_price:
                    should_close = True
                    self.logger.info(f"Long take profit hit: entry={entry_price}, "
                                   f"current={current_price}, target={take_profit_price}")

            elif signal == 'SHORT':
                take_profit_price = entry_price - take_profit_distance
                if current_price <= take_profit_price:
                    should_close = True
                    self.logger.info(f"Short take profit hit: entry={entry_price}, "
                                   f"current={current_price}, target={take_profit_price}")
            
            if should_close:
                self._close_position(position_id)
    
    def _check_martingale_conditions(self):
        """Check if martingale scaling should be applied to any positions."""
        for position_id, position_info in self.positions_info.items():
            if position_info.get('status', 'active') != 'active':
                continue
            
            current_martingale_level = self.martingale_levels.get(position_id, 0)
            
            # Check if we've reached maximum martingale levels
            if current_martingale_level >= self.params.max_martingale_levels:
                continue
            
            current_price = float(self.data.close[0])
            entry_price = float(position_info['entry_price'])
            signal = position_info['signal']

            # Calculate volatility-based distance for martingale trigger
            martingale_distance = float(self._calculate_volatility_distance())

            should_martingale = False

            if signal == 'LONG' and current_price <= (entry_price - martingale_distance):
                should_martingale = True
                self.logger.info(f"Long martingale trigger: entry={entry_price}, "
                               f"current={current_price}, distance={martingale_distance}")

            elif signal == 'SHORT' and current_price >= (entry_price + martingale_distance):
                should_martingale = True
                self.logger.info(f"Short martingale trigger: entry={entry_price}, "
                                f"current={current_price}, distance={martingale_distance}")
            
            if should_martingale:
                self._apply_martingale(position_id)
    
    def _apply_martingale(self, position_id: int):
        """Apply martingale scaling to a position."""
        position_info = self.positions_info[position_id]
        current_level = self.martingale_levels[position_id]
        new_level = current_level + 1
        
        # Calculate new position size
        additional_size = self._calculate_position_size(new_level)
        
        signal = position_info['signal']
        
        if signal == 'LONG':
            order = self.buy(size=additional_size)
        else:
            order = self.sell(size=additional_size)
        
        if order:
            # Update position tracking
            self.martingale_levels[position_id] = new_level
            self.position_sizes[position_id] += additional_size
            
            # Update entry price (average down/up)
            total_size = self.position_sizes[position_id]
            old_size = total_size - additional_size
            old_price = float(self.entry_prices[position_id])
            new_price = float(self.data.close[0])

            # Calculate weighted average entry price
            avg_price = ((old_price * old_size) + (new_price * additional_size)) / total_size
            self.entry_prices[position_id] = avg_price
            position_info['entry_price'] = avg_price
            
            self.logger.info(f"Martingale applied: level={new_level}, "
                           f"additional_size={additional_size}, new_avg_price={avg_price}")
    
    def _close_position(self, position_id: int):
        """Close a position and update tracking."""
        position_info = self.positions_info[position_id]
        
        if position_info['signal'] == 'LONG':
            self.close()  # Close long position
        else:
            self.close()  # Close short position
        
        # Mark position as closed
        position_info['status'] = 'closed'
        exit_price = float(self.data.close[0])
        position_info['exit_price'] = exit_price
        position_info['exit_timestamp'] = self.data.datetime.datetime(0)

        # Calculate P&L
        entry_price = float(position_info['entry_price'])
        size = self.position_sizes[position_id]
        
        if position_info['signal'] == 'LONG':
            pnl = (exit_price - entry_price) * size
        else:
            pnl = (entry_price - exit_price) * size
        
        position_info['pnl'] = pnl
        self.total_pnl += pnl
        
        # Update trade statistics
        self.trade_count += 1
        if pnl > 0:
            self.winning_trades += 1
        else:
            self.losing_trades += 1
        
        self.logger.info(f"Position closed: P&L={pnl:.2f}, "
                        f"entry={entry_price}, exit={exit_price}")
    
    def _calculate_volatility_distance(self) -> float:
        """Calculate volatility-based distance for take profit and martingale triggers."""
        if self.params.volatility_method == 'ATR':
            return self.atr[0] * self.params.volatility_multiplier
        elif self.params.volatility_method == 'BOLLINGER':
            return (self.bollinger.lines.top[0] - self.bollinger.lines.bot[0]) / 2
        elif self.params.volatility_method == 'STDDEV':
            return self.stddev[0] * self.params.volatility_multiplier
        else:
            # Default to ATR
            return self.atr[0] * self.params.volatility_multiplier
    
    def _check_max_drawdown(self) -> bool:
        """Check if maximum drawdown has been reached."""
        current_value = self.broker.get_value()
        initial_value = self.config.get_initial_cash()
        
        # Calculate current drawdown
        peak_value = max(initial_value, current_value)
        current_drawdown = (peak_value - current_value) / peak_value
        
        self.max_drawdown_reached = max(self.max_drawdown_reached, current_drawdown)
        
        return current_drawdown >= self.params.max_drawdown
    
    def notify_order(self, order):
        """Notification of order status changes."""
        if order.status in [order.Completed]:
            if order.isbuy():
                self.logger.debug(f"Buy order completed: price={order.executed.price}, "
                                f"size={order.executed.size}")
            else:
                self.logger.debug(f"Sell order completed: price={order.executed.price}, "
                                f"size={order.executed.size}")
        
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.logger.warning(f"Order failed: status={order.status}")
    
    def notify_trade(self, trade):
        """Notification of trade completion."""
        if trade.isclosed:
            self.logger.info(f"Trade closed: P&L={trade.pnl:.2f}, "
                           f"commission={trade.commission:.2f}")
    
    def get_strategy_stats(self) -> Dict[str, Any]:
        """Get strategy performance statistics."""
        return {
            'total_trades': self.trade_count,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'win_rate': self.winning_trades / max(self.trade_count, 1),
            'total_pnl': self.total_pnl,
            'max_drawdown': self.max_drawdown_reached,
            'active_positions': len([p for p in self.positions_info.values() 
                                   if p.get('status', 'active') == 'active']),
            'positions_info': self.positions_info
        }
