"""
Strategy module for the Forex backtesting system.
Contains base strategy class and strategy implementations.
"""

from .base_strategy import BaseStrategy
from .strategy_factory import (
    get_strategy,
    STRATEGY_REGISTRY,
    list_strategies,
    print_available_strategies,
    register_strategy,
    get_strategy_info
)

__all__ = [
    'BaseStrategy',
    'get_strategy',
    'STRATEGY_REGISTRY',
    'list_strategies',
    'print_available_strategies',
    'register_strategy',
    'get_strategy_info'
]
