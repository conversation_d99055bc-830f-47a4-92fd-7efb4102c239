"""
Minimal strategy that should work with Backtrader.
"""

import backtrader as bt
import logging


class MinimalStrategy(bt.Strategy):
    """Minimal strategy for testing."""
    
    params = (
        ('ema_fast', 20),
        ('ema_slow', 50),
    )
    
    def __init__(self):
        """Initialize the strategy."""
        # Create indicators
        self.ema_fast = bt.indicators.EMA(period=self.params.ema_fast)
        self.ema_slow = bt.indicators.EMA(period=self.params.ema_slow)
        self.crossover = bt.indicators.CrossOver(self.ema_fast, self.ema_slow)
        
        print(f"MinimalStrategy initialized with fast={self.params.ema_fast}, slow={self.params.ema_slow}")
    
    def next(self):
        """Strategy logic."""
        if len(self.data) < self.params.ema_slow:
            return
        
        if not self.position:
            if self.crossover > 0:
                self.buy()
        else:
            if self.crossover < 0:
                self.close()
    
    def get_strategy_stats(self):
        """Get basic strategy statistics."""
        return {
            'strategy_name': 'Minimal Strategy'
        }
