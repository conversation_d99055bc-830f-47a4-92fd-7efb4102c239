"""
Simple EMA Crossover Strategy that works with Backtrader.
Simplified version without complex base class.
"""

import backtrader as bt
import logging
from config import get_config


class SimpleEMAStrategy(bt.Strategy):
    """Simple EMA Crossover Strategy."""
    
    params = (
        ('ema_fast', 55),
        ('ema_slow', 89),
        ('position_size_percent', 0.02),
    )
    
    def __init__(self):
        """Initialize the strategy."""
        self.config = get_config()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Create indicators
        self.ema_fast = bt.indicators.EMA(period=self.params.ema_fast)
        self.ema_slow = bt.indicators.EMA(period=self.params.ema_slow)
        
        # Crossover signal
        self.crossover = bt.indicators.CrossOver(self.ema_fast, self.ema_slow)
        
        # ATR for volatility
        self.atr = bt.indicators.AverageTrueRange(period=14)
        
        # Track trades
        self.trade_count = 0
        
        self.logger.info("Simple EMA strategy initialized")
    
    def next(self):
        """Strategy logic."""
        try:
            # Only trade if we have enough data
            if len(self.data) < self.params.ema_slow:
                return
            
            # Entry logic
            if not self.position:
                if self.crossover > 0:  # Fast EMA crosses above slow EMA
                    size = self._calculate_position_size()
                    self.buy(size=size)
                    self.trade_count += 1
                    self.logger.info(f"Buy signal #{self.trade_count} at {self.data.close[0]:.5f}, size={size:.2f}")
                
                elif self.crossover < 0:  # Fast EMA crosses below slow EMA
                    size = self._calculate_position_size()
                    self.sell(size=size)
                    self.trade_count += 1
                    self.logger.info(f"Sell signal #{self.trade_count} at {self.data.close[0]:.5f}, size={size:.2f}")
            
            else:
                # Exit logic - simple take profit based on ATR
                entry_price = self.position.price
                current_price = float(self.data.close[0])
                atr_value = float(self.atr[0])
                
                if self.position.size > 0:  # Long position
                    take_profit_price = entry_price + (atr_value * 2)
                    if current_price >= take_profit_price:
                        self.close()
                        self.logger.info(f"Long take profit at {current_price:.5f}")
                
                elif self.position.size < 0:  # Short position
                    take_profit_price = entry_price - (atr_value * 2)
                    if current_price <= take_profit_price:
                        self.close()
                        self.logger.info(f"Short take profit at {current_price:.5f}")
        
        except Exception as e:
            self.logger.error(f"Error in strategy execution: {e}")
    
    def _calculate_position_size(self):
        """Calculate position size based on account balance."""
        try:
            account_value = self.broker.get_value()
            position_value = account_value * self.params.position_size_percent
            current_price = float(self.data.close[0])
            position_size = position_value / current_price
            return max(position_size, 0.01)  # Minimum position size
        except Exception as e:
            self.logger.error(f"Error calculating position size: {e}")
            return 0.01
    
    def notify_order(self, order):
        """Notification of order status changes."""
        if order.status in [order.Completed]:
            if order.isbuy():
                self.logger.debug(f"Buy order completed: price={order.executed.price:.5f}, size={order.executed.size:.2f}")
            else:
                self.logger.debug(f"Sell order completed: price={order.executed.price:.5f}, size={order.executed.size:.2f}")
        
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.logger.warning(f"Order failed: status={order.status}")
    
    def notify_trade(self, trade):
        """Notification of trade completion."""
        if trade.isclosed:
            self.logger.info(f"Trade closed: P&L={trade.pnl:.2f}, commission={trade.commission:.2f}")
    
    def get_strategy_stats(self):
        """Get basic strategy statistics."""
        return {
            'total_trades': self.trade_count,
            'strategy_name': 'Simple EMA Crossover'
        }
