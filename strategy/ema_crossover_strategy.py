"""
EMA Crossover Strategy Implementation.
Uses EMA 55 and EMA 89 crossover signals with volatility-based position management and martingale scaling.
"""

import backtrader as bt
import logging
from typing import Optional
from .base_strategy import BaseStrategy


class EMACrossoverStrategy(BaseStrategy):
    """
    EMA Crossover Strategy using EMA 55 and EMA 89.
    
    Entry Signals:
    - Long: When EMA 55 crosses above EMA 89
    - Short: When EMA 55 crosses below EMA 89
    
    Exit Strategy:
    - Take profit based on volatility (ATR) distance
    - No traditional stop loss - positions remain open until profitable
    - Martingale scaling when position moves against trend
    """
    
    params = (
        ('ema_fast', 55),
        ('ema_slow', 89),
        ('atr_period', 14),
        ('volatility_multiplier', 2.0),
        ('max_positions', 5),
        ('position_size_percent', 0.02),
        ('martingale_multiplier', 2.0),
        ('max_martingale_levels', 5),
        ('max_drawdown', 0.20),
        ('volatility_method', 'ATR'),
        ('trend_confirmation', True),  # Use trend confirmation
        ('min_crossover_strength', 0.0001),  # Minimum price difference for valid crossover
    )
    
    def setup_strategy_indicators(self):
        """Setup EMA crossover specific indicators."""
        try:
            # Fast and slow EMAs
            self.ema_fast = bt.indicators.EMA(period=self.params.ema_fast)
            self.ema_slow = bt.indicators.EMA(period=self.params.ema_slow)

            # EMA difference for trend strength
            self.ema_diff = self.ema_fast - self.ema_slow

            # Additional trend confirmation indicators
            if self.params.trend_confirmation:
                # Longer-term EMA for trend confirmation (200 period)
                self.ema_trend = bt.indicators.EMA(period=200)

                # MACD for additional confirmation
                self.macd = bt.indicators.MACD(
                    period_me1=12,
                    period_me2=26,
                    period_signal=9
                )

            # Price momentum indicator
            self.momentum = bt.indicators.Momentum(period=10)

        except Exception as e:
            self.logger.error(f"Error setting up EMA indicators: {e}")
            self.logger.warning("Continuing without some indicators")
        
        self.logger.info(f"EMA Crossover indicators initialized: "
                        f"Fast EMA={self.params.ema_fast}, Slow EMA={self.params.ema_slow}")
    
    def setup_signals(self):
        """Setup crossover signals."""
        try:
            # Check if indicators are available
            if not hasattr(self, 'ema_fast') or not hasattr(self, 'ema_slow'):
                self.logger.warning("EMAs not available for signal setup")
                return

            # Primary crossover signal
            self.crossover = bt.indicators.CrossOver(self.ema_fast, self.ema_slow)

            # Signal strength (how far apart the EMAs are)
            self.signal_strength = abs(self.ema_diff) / self.data.close

        except Exception as e:
            self.logger.error(f"Error setting up signals: {e}")
            self.logger.warning("Continuing without some signals")
        
        self.logger.debug("EMA crossover signals initialized")
    
    def get_entry_signal(self) -> Optional[str]:
        """
        Determine entry signal based on EMA crossover.
        
        Returns:
            'LONG' for bullish crossover, 'SHORT' for bearish crossover, None otherwise
        """
        # Check if we have enough data
        if len(self.data) < max(self.params.ema_fast, self.params.ema_slow):
            return None
        
        # Get current crossover signal
        current_crossover = self.crossover[0]
        
        # Check for valid crossover with minimum strength
        if abs(self.ema_diff[0]) < self.params.min_crossover_strength:
            return None
        
        signal = None
        
        # Bullish crossover: Fast EMA crosses above Slow EMA
        if current_crossover > 0:
            if self._validate_long_signal():
                signal = 'LONG'
                self.logger.info(f"Long signal detected: EMA Fast={self.ema_fast[0]:.5f}, "
                               f"EMA Slow={self.ema_slow[0]:.5f}, Price={self.data.close[0]:.5f}")
        
        # Bearish crossover: Fast EMA crosses below Slow EMA
        elif current_crossover < 0:
            if self._validate_short_signal():
                signal = 'SHORT'
                self.logger.info(f"Short signal detected: EMA Fast={self.ema_fast[0]:.5f}, "
                                f"EMA Slow={self.ema_slow[0]:.5f}, Price={self.data.close[0]:.5f}")
        
        return signal
    
    def _validate_long_signal(self) -> bool:
        """
        Validate long entry signal with additional confirmation.
        
        Returns:
            True if long signal is valid
        """
        # Basic validation: Fast EMA should be above Slow EMA
        if self.ema_fast[0] <= self.ema_slow[0]:
            return False
        
        # Trend confirmation if enabled
        if self.params.trend_confirmation:
            # Price should be above long-term trend EMA
            if self.data.close[0] < self.ema_trend[0]:
                self.logger.debug("Long signal rejected: Price below trend EMA")
                return False
            
            # MACD should be positive or improving
            if hasattr(self, 'macd') and self.macd.macd[0] < self.macd.signal[0]:
                # Allow if MACD is improving (histogram increasing)
                if len(self.macd.histo) > 1 and self.macd.histo[0] <= self.macd.histo[-1]:
                    self.logger.debug("Long signal rejected: MACD not confirming")
                    return False
        
        # Check momentum
        if self.momentum[0] < 0:
            self.logger.debug("Long signal rejected: Negative momentum")
            return False

        # Check volatility - avoid entries during extreme volatility
        try:
            current_atr = float(self.atr[0])
            # Simplified volatility check
            if current_atr > 0.01:  # Simple threshold
                self.logger.debug("Long signal rejected: High volatility")
                return False
        except Exception as e:
            self.logger.debug(f"Volatility check failed: {e}")
            # Continue without volatility check
        
        return True
    
    def _validate_short_signal(self) -> bool:
        """
        Validate short entry signal with additional confirmation.
        
        Returns:
            True if short signal is valid
        """
        # Basic validation: Fast EMA should be below Slow EMA
        if self.ema_fast[0] >= self.ema_slow[0]:
            return False
        
        # Trend confirmation if enabled
        if self.params.trend_confirmation:
            # Price should be below long-term trend EMA
            if self.data.close[0] > self.ema_trend[0]:
                self.logger.debug("Short signal rejected: Price above trend EMA")
                return False
            
            # MACD should be negative or deteriorating
            if hasattr(self, 'macd') and self.macd.macd[0] > self.macd.signal[0]:
                # Allow if MACD is deteriorating (histogram decreasing)
                if len(self.macd.histo) > 1 and self.macd.histo[0] >= self.macd.histo[-1]:
                    self.logger.debug("Short signal rejected: MACD not confirming")
                    return False
        
        # Check momentum
        if self.momentum[0] > 0:
            self.logger.debug("Short signal rejected: Positive momentum")
            return False

        # Check volatility - avoid entries during extreme volatility
        try:
            current_atr = float(self.atr[0])
            # Simplified volatility check
            if current_atr > 0.01:  # Simple threshold
                self.logger.debug("Short signal rejected: High volatility")
                return False
        except Exception as e:
            self.logger.debug(f"Volatility check failed: {e}")
            # Continue without volatility check
        
        return True
    
    def get_strategy_description(self) -> str:
        """Get a description of the strategy."""
        return (f"EMA Crossover Strategy using EMA {self.params.ema_fast} and "
                f"EMA {self.params.ema_slow} with {self.params.volatility_method} "
                f"volatility measurement and martingale scaling")
    
    def get_strategy_parameters(self) -> dict:
        """Get current strategy parameters."""
        return {
            'ema_fast': self.params.ema_fast,
            'ema_slow': self.params.ema_slow,
            'atr_period': self.params.atr_period,
            'volatility_multiplier': self.params.volatility_multiplier,
            'max_positions': self.params.max_positions,
            'position_size_percent': self.params.position_size_percent,
            'martingale_multiplier': self.params.martingale_multiplier,
            'max_martingale_levels': self.params.max_martingale_levels,
            'volatility_method': self.params.volatility_method,
            'trend_confirmation': self.params.trend_confirmation,
            'min_crossover_strength': self.params.min_crossover_strength,
        }
    
    def get_current_indicators(self) -> dict:
        """Get current indicator values for analysis."""
        if len(self.data) < max(self.params.ema_fast, self.params.ema_slow):
            return {}
        
        indicators = {
            'ema_fast': self.ema_fast[0],
            'ema_slow': self.ema_slow[0],
            'ema_diff': self.ema_diff[0],
            'atr': self.atr[0],
            'signal_strength': self.signal_strength[0],
            'momentum': self.momentum[0],
            'crossover': self.crossover[0],
        }
        
        if self.params.trend_confirmation and hasattr(self, 'ema_trend'):
            indicators['ema_trend'] = self.ema_trend[0]
            
        if hasattr(self, 'macd'):
            indicators.update({
                'macd': self.macd.macd[0],
                'macd_signal': self.macd.signal[0],
                'macd_histogram': self.macd.histo[0],
            })
        
        return indicators
