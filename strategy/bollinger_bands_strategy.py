"""
Bollinger Bands Mean Reversion Strategy Implementation.
Uses Bollinger Bands for mean reversion trading with volatility-based position management.
"""

import backtrader as bt
import logging
from typing import Optional
from .base_strategy import BaseStrategy


class BollingerBandsStrategy(BaseStrategy):
    """
    Bollinger Bands Mean Reversion Strategy.
    
    Entry Signals:
    - Long: Price touches or breaks below lower Bollinger Band
    - Short: Price touches or breaks above upper Bollinger Band
    
    Exit Strategy:
    - Take profit when price reaches middle band (SMA)
    - Alternative exit based on volatility distance
    """
    
    params = (
        ('bb_period', 20),
        ('bb_devfactor', 2.0),
        ('rsi_period', 14),
        ('rsi_oversold', 30),
        ('rsi_overbought', 70),
        ('atr_period', 14),
        ('volatility_multiplier', 1.5),
        ('max_positions', 5),
        ('position_size_percent', 0.02),
        ('martingale_multiplier', 2.0),
        ('max_martingale_levels', 5),
        ('max_drawdown', 0.20),
        ('volatility_method', 'BOLLINGER'),
        ('use_rsi_filter', True),  # Use RSI as additional filter
        ('exit_at_middle', True),  # Exit when price reaches middle band
        ('band_touch_threshold', 0.001),  # Threshold for band touch detection
    )
    
    def setup_strategy_indicators(self):
        """Setup Bollinger Bands and related indicators."""
        # Bollinger Bands
        self.bollinger = bt.indicators.BollingerBands(
            period=self.params.bb_period,
            devfactor=self.params.bb_devfactor
        )
        
        # RSI for additional filtering
        if self.params.use_rsi_filter:
            self.rsi = bt.indicators.RSI(period=self.params.rsi_period)
        
        # Price position within bands (0 = lower band, 1 = upper band)
        self.bb_position = (self.data.close - self.bollinger.lines.bot) / \
                          (self.bollinger.lines.top - self.bollinger.lines.bot)
        
        # Bollinger Band width (volatility measure)
        self.bb_width = (self.bollinger.lines.top - self.bollinger.lines.bot) / \
                       self.bollinger.lines.mid
        
        # Price distance from middle band
        self.distance_from_mid = abs(self.data.close - self.bollinger.lines.mid) / \
                                self.bollinger.lines.mid
        
        # Williams %R for additional momentum
        self.williams_r = bt.indicators.WilliamsR(period=14)
        
        self.logger.info(f"Bollinger Bands indicators initialized: "
                        f"period={self.params.bb_period}, devfactor={self.params.bb_devfactor}")
    
    def setup_signals(self):
        """Setup Bollinger Bands signals."""
        # Band touch signals
        self.lower_band_touch = self.data.close <= (self.bollinger.lines.bot * 
                                                   (1 + self.params.band_touch_threshold))
        self.upper_band_touch = self.data.close >= (self.bollinger.lines.top * 
                                                   (1 - self.params.band_touch_threshold))
        
        # Middle band cross signals
        self.middle_cross_up = bt.indicators.CrossOver(self.data.close, self.bollinger.lines.mid)
        
        self.logger.debug("Bollinger Bands signals initialized")
    
    def get_entry_signal(self) -> Optional[str]:
        """
        Determine entry signal based on Bollinger Bands mean reversion.
        
        Returns:
            'LONG' for oversold signal, 'SHORT' for overbought signal, None otherwise
        """
        # Check if we have enough data
        if len(self.data) < self.params.bb_period:
            return None
        
        current_price = self.data.close[0]
        lower_band = self.bollinger.lines.bot[0]
        upper_band = self.bollinger.lines.top[0]
        bb_position = self.bb_position[0]
        
        signal = None
        
        # Long signal: Price near or below lower band (oversold)
        if self._check_long_conditions(current_price, lower_band, bb_position):
            if self._validate_long_entry():
                signal = 'LONG'
                self.logger.info(f"Long signal: Price={current_price:.5f}, "
                               f"Lower Band={lower_band:.5f}, BB Position={bb_position:.3f}")
        
        # Short signal: Price near or above upper band (overbought)
        elif self._check_short_conditions(current_price, upper_band, bb_position):
            if self._validate_short_entry():
                signal = 'SHORT'
                self.logger.info(f"Short signal: Price={current_price:.5f}, "
                                f"Upper Band={upper_band:.5f}, BB Position={bb_position:.3f}")
        
        return signal
    
    def _check_long_conditions(self, price: float, lower_band: float, bb_position: float) -> bool:
        """Check if long entry conditions are met."""
        # Price should be near or below lower band
        band_condition = bb_position <= 0.1  # Within 10% of lower band
        
        # Alternative: direct price comparison
        price_condition = price <= lower_band * (1 + self.params.band_touch_threshold)
        
        return band_condition or price_condition
    
    def _check_short_conditions(self, price: float, upper_band: float, bb_position: float) -> bool:
        """Check if short entry conditions are met."""
        # Price should be near or above upper band
        band_condition = bb_position >= 0.9  # Within 10% of upper band
        
        # Alternative: direct price comparison
        price_condition = price >= upper_band * (1 - self.params.band_touch_threshold)
        
        return band_condition or price_condition
    
    def _validate_long_entry(self) -> bool:
        """Validate long entry with additional filters."""
        # RSI filter: should be oversold
        if self.params.use_rsi_filter and hasattr(self, 'rsi'):
            if self.rsi[0] > self.params.rsi_oversold:
                self.logger.debug(f"Long signal rejected: RSI not oversold ({self.rsi[0]:.2f})")
                return False
        
        # Williams %R should indicate oversold
        if self.williams_r[0] > -80:  # Not oversold enough
            self.logger.debug(f"Long signal rejected: Williams %R not oversold ({self.williams_r[0]:.2f})")
            return False
        
        # Bollinger Band width should not be too narrow (low volatility)
        if self.bb_width[0] < 0.01:  # Less than 1% width
            self.logger.debug("Long signal rejected: Low volatility (narrow bands)")
            return False
        
        # Check for recent volatility expansion
        if len(self.bb_width) > 5:
            recent_avg_width = sum(self.bb_width.get(ago=i) for i in range(5)) / 5
            if self.bb_width[0] < recent_avg_width * 0.8:  # Current width < 80% of recent average
                self.logger.debug("Long signal rejected: Contracting volatility")
                return False
        
        return True
    
    def _validate_short_entry(self) -> bool:
        """Validate short entry with additional filters."""
        # RSI filter: should be overbought
        if self.params.use_rsi_filter and hasattr(self, 'rsi'):
            if self.rsi[0] < self.params.rsi_overbought:
                self.logger.debug(f"Short signal rejected: RSI not overbought ({self.rsi[0]:.2f})")
                return False
        
        # Williams %R should indicate overbought
        if self.williams_r[0] < -20:  # Not overbought enough
            self.logger.debug(f"Short signal rejected: Williams %R not overbought ({self.williams_r[0]:.2f})")
            return False
        
        # Bollinger Band width should not be too narrow (low volatility)
        if self.bb_width[0] < 0.01:  # Less than 1% width
            self.logger.debug("Short signal rejected: Low volatility (narrow bands)")
            return False
        
        # Check for recent volatility expansion
        if len(self.bb_width) > 5:
            recent_avg_width = sum(self.bb_width.get(ago=i) for i in range(5)) / 5
            if self.bb_width[0] < recent_avg_width * 0.8:  # Current width < 80% of recent average
                self.logger.debug("Short signal rejected: Contracting volatility")
                return False
        
        return True
    
    def _manage_positions(self):
        """Override position management to include Bollinger Bands exits."""
        # Call parent position management first
        super()._manage_positions()
        
        # Additional Bollinger Bands exit logic
        if not self.params.exit_at_middle:
            return
        
        for position_id, position_info in self.positions_info.items():
            if position_info.get('status', 'active') != 'active':
                continue
            
            current_price = self.data.close[0]
            middle_band = self.bollinger.lines.mid[0]
            signal = position_info['signal']
            
            should_close = False
            
            # Close long positions when price reaches middle band from below
            if signal == 'LONG' and current_price >= middle_band:
                should_close = True
                self.logger.info(f"Long position closed at middle band: "
                               f"price={current_price:.5f}, middle={middle_band:.5f}")
            
            # Close short positions when price reaches middle band from above
            elif signal == 'SHORT' and current_price <= middle_band:
                should_close = True
                self.logger.info(f"Short position closed at middle band: "
                                f"price={current_price:.5f}, middle={middle_band:.5f}")
            
            if should_close:
                self._close_position(position_id)
    
    def get_strategy_description(self) -> str:
        """Get a description of the strategy."""
        return (f"Bollinger Bands Mean Reversion Strategy using "
                f"BB({self.params.bb_period}, {self.params.bb_devfactor}) "
                f"with {'RSI filter' if self.params.use_rsi_filter else 'no filter'}")
    
    def get_current_indicators(self) -> dict:
        """Get current indicator values for analysis."""
        if len(self.data) < self.params.bb_period:
            return {}
        
        indicators = {
            'bb_upper': self.bollinger.lines.top[0],
            'bb_middle': self.bollinger.lines.mid[0],
            'bb_lower': self.bollinger.lines.bot[0],
            'bb_position': self.bb_position[0],
            'bb_width': self.bb_width[0],
            'distance_from_mid': self.distance_from_mid[0],
            'williams_r': self.williams_r[0],
            'atr': self.atr[0],
        }
        
        if self.params.use_rsi_filter and hasattr(self, 'rsi'):
            indicators['rsi'] = self.rsi[0]
        
        return indicators
