"""
RSI + MACD Strategy Implementation.
Combines RSI oversold/overbought levels with MACD crossover signals.
"""

import backtrader as bt
import logging
from typing import Optional
from .base_strategy import BaseStrategy


class RSIMACDStrategy(BaseStrategy):
    """
    RSI + MACD Strategy combining momentum indicators.
    
    Entry Signals:
    - Long: RSI oversold (< 30) + MACD bullish crossover
    - Short: RSI overbought (> 70) + MACD bearish crossover
    
    Exit Strategy:
    - Take profit based on volatility (ATR) distance
    - RSI extreme levels (< 20 for shorts, > 80 for longs)
    """
    
    params = (
        ('rsi_period', 14),
        ('rsi_oversold', 30),
        ('rsi_overbought', 70),
        ('rsi_extreme_low', 20),
        ('rsi_extreme_high', 80),
        ('macd_fast', 12),
        ('macd_slow', 26),
        ('macd_signal', 9),
        ('atr_period', 14),
        ('volatility_multiplier', 2.0),
        ('max_positions', 5),
        ('position_size_percent', 0.02),
        ('martingale_multiplier', 2.0),
        ('max_martingale_levels', 5),
        ('max_drawdown', 0.20),
        ('volatility_method', 'ATR'),
        ('require_both_signals', True),  # Require both RSI and MACD confirmation
    )
    
    def setup_strategy_indicators(self):
        """Setup RSI and MACD indicators."""
        # RSI indicator
        self.rsi = bt.indicators.RSI(period=self.params.rsi_period)
        
        # MACD indicator
        self.macd = bt.indicators.MACD(
            period_me1=self.params.macd_fast,
            period_me2=self.params.macd_slow,
            period_signal=self.params.macd_signal
        )
        
        # Additional momentum indicators
        self.stochastic = bt.indicators.Stochastic(
            period=14,
            period_dfast=3,
            period_dslow=3
        )
        
        # Price rate of change
        self.roc = bt.indicators.RateOfChange(period=10)
        
        self.logger.info(f"RSI+MACD indicators initialized: "
                        f"RSI period={self.params.rsi_period}, "
                        f"MACD({self.params.macd_fast},{self.params.macd_slow},{self.params.macd_signal})")
    
    def setup_signals(self):
        """Setup RSI and MACD signals."""
        # MACD crossover signals
        self.macd_crossover = bt.indicators.CrossOver(self.macd.macd, self.macd.signal)
        
        # RSI level signals
        self.rsi_oversold = self.rsi < self.params.rsi_oversold
        self.rsi_overbought = self.rsi > self.params.rsi_overbought
        
        self.logger.debug("RSI+MACD signals initialized")
    
    def get_entry_signal(self) -> Optional[str]:
        """
        Determine entry signal based on RSI and MACD combination.
        
        Returns:
            'LONG' for bullish signal, 'SHORT' for bearish signal, None otherwise
        """
        # Check if we have enough data
        if len(self.data) < max(self.params.rsi_period, self.params.macd_slow):
            return None
        
        current_rsi = self.rsi[0]
        current_macd = self.macd.macd[0]
        current_signal = self.macd.signal[0]
        macd_crossover = self.macd_crossover[0]
        
        signal = None
        
        # Long signal: RSI oversold + MACD bullish crossover
        if self._check_long_conditions(current_rsi, macd_crossover):
            if self._validate_long_entry():
                signal = 'LONG'
                self.logger.info(f"Long signal: RSI={current_rsi:.2f}, "
                               f"MACD={current_macd:.5f}, Signal={current_signal:.5f}")
        
        # Short signal: RSI overbought + MACD bearish crossover
        elif self._check_short_conditions(current_rsi, macd_crossover):
            if self._validate_short_entry():
                signal = 'SHORT'
                self.logger.info(f"Short signal: RSI={current_rsi:.2f}, "
                                f"MACD={current_macd:.5f}, Signal={current_signal:.5f}")
        
        return signal
    
    def _check_long_conditions(self, rsi: float, macd_crossover: float) -> bool:
        """Check if long entry conditions are met."""
        rsi_condition = rsi < self.params.rsi_oversold
        macd_condition = macd_crossover > 0  # MACD line crosses above signal line
        
        if self.params.require_both_signals:
            return rsi_condition and macd_condition
        else:
            return rsi_condition or macd_condition
    
    def _check_short_conditions(self, rsi: float, macd_crossover: float) -> bool:
        """Check if short entry conditions are met."""
        rsi_condition = rsi > self.params.rsi_overbought
        macd_condition = macd_crossover < 0  # MACD line crosses below signal line
        
        if self.params.require_both_signals:
            return rsi_condition and macd_condition
        else:
            return rsi_condition or macd_condition
    
    def _validate_long_entry(self) -> bool:
        """Validate long entry with additional filters."""
        # Check if MACD is below zero (coming from negative territory)
        if self.macd.macd[0] > 0:
            self.logger.debug("Long signal rejected: MACD already positive")
            return False
        
        # Check stochastic for additional confirmation
        if hasattr(self, 'stochastic'):
            if self.stochastic.percK[0] > 80:  # Already overbought
                self.logger.debug("Long signal rejected: Stochastic overbought")
                return False
        
        # Check price momentum
        if self.roc[0] < -5:  # Significant negative momentum
            self.logger.debug("Long signal rejected: Strong negative momentum")
            return False
        
        return True
    
    def _validate_short_entry(self) -> bool:
        """Validate short entry with additional filters."""
        # Check if MACD is above zero (coming from positive territory)
        if self.macd.macd[0] < 0:
            self.logger.debug("Short signal rejected: MACD already negative")
            return False
        
        # Check stochastic for additional confirmation
        if hasattr(self, 'stochastic'):
            if self.stochastic.percK[0] < 20:  # Already oversold
                self.logger.debug("Short signal rejected: Stochastic oversold")
                return False
        
        # Check price momentum
        if self.roc[0] > 5:  # Significant positive momentum
            self.logger.debug("Short signal rejected: Strong positive momentum")
            return False
        
        return True
    
    def _manage_positions(self):
        """Override position management to include RSI-based exits."""
        # Call parent position management first
        super()._manage_positions()
        
        # Additional RSI-based exit logic
        for position_id, position_info in self.positions_info.items():
            if position_info.get('status', 'active') != 'active':
                continue
            
            current_rsi = self.rsi[0]
            signal = position_info['signal']
            
            should_close = False
            
            # Close long positions when RSI reaches extreme high
            if signal == 'LONG' and current_rsi > self.params.rsi_extreme_high:
                should_close = True
                self.logger.info(f"Long position closed due to RSI extreme: {current_rsi:.2f}")
            
            # Close short positions when RSI reaches extreme low
            elif signal == 'SHORT' and current_rsi < self.params.rsi_extreme_low:
                should_close = True
                self.logger.info(f"Short position closed due to RSI extreme: {current_rsi:.2f}")
            
            if should_close:
                self._close_position(position_id)
    
    def get_strategy_description(self) -> str:
        """Get a description of the strategy."""
        return (f"RSI+MACD Strategy using RSI({self.params.rsi_period}) "
                f"levels {self.params.rsi_oversold}/{self.params.rsi_overbought} "
                f"with MACD({self.params.macd_fast},{self.params.macd_slow},{self.params.macd_signal}) crossovers")
    
    def get_current_indicators(self) -> dict:
        """Get current indicator values for analysis."""
        if len(self.data) < max(self.params.rsi_period, self.params.macd_slow):
            return {}
        
        return {
            'rsi': self.rsi[0],
            'macd': self.macd.macd[0],
            'macd_signal': self.macd.signal[0],
            'macd_histogram': self.macd.histo[0],
            'macd_crossover': self.macd_crossover[0],
            'stochastic_k': self.stochastic.percK[0] if hasattr(self, 'stochastic') else None,
            'stochastic_d': self.stochastic.percD[0] if hasattr(self, 'stochastic') else None,
            'roc': self.roc[0],
            'atr': self.atr[0],
        }
