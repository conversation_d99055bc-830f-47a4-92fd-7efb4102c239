"""
Support/Resistance Breakout Strategy Implementation.
Identifies key support and resistance levels and trades breakouts with volume confirmation.
"""

import backtrader as bt
import logging
from typing import Optional, List, Tuple
import numpy as np
from .base_strategy import BaseStrategy


class BreakoutStrategy(BaseStrategy):
    """
    Support/Resistance Breakout Strategy.
    
    Entry Signals:
    - Long: Price breaks above resistance with volume confirmation
    - Short: Price breaks below support with volume confirmation
    
    Exit Strategy:
    - Take profit based on volatility distance
    - Stop loss if price returns to breakout level
    """
    
    params = (
        ('lookback_period', 20),  # Period to look for support/resistance
        ('min_touches', 2),  # Minimum touches to confirm level
        ('breakout_threshold', 0.0005),  # Minimum breakout distance
        ('volume_multiplier', 1.5),  # Volume should be X times average
        ('volume_period', 20),  # Period for average volume calculation
        ('atr_period', 14),
        ('volatility_multiplier', 2.0),
        ('max_positions', 5),
        ('position_size_percent', 0.02),
        ('martingale_multiplier', 2.0),
        ('max_martingale_levels', 5),
        ('max_drawdown', 0.20),
        ('volatility_method', 'ATR'),
        ('use_volume_filter', True),  # Require volume confirmation
        ('max_level_age', 50),  # Maximum age of support/resistance level
    )
    
    def __init__(self):
        """Initialize the breakout strategy."""
        super().__init__()
        
        # Support and resistance levels tracking
        self.support_levels = []  # List of (price, strength, age) tuples
        self.resistance_levels = []  # List of (price, strength, age) tuples
        self.last_update_bar = 0
        
    def setup_strategy_indicators(self):
        """Setup breakout-specific indicators."""
        # Volume indicators
        if self.params.use_volume_filter:
            self.volume_sma = bt.indicators.SMA(
                self.data.volume, 
                period=self.params.volume_period
            )
            self.volume_ratio = self.data.volume / self.volume_sma
        
        # Price momentum
        self.momentum = bt.indicators.Momentum(period=10)
        
        # Donchian Channels for additional breakout confirmation
        self.donchian = bt.indicators.DonchianChannels(period=self.params.lookback_period)
        
        # RSI for overbought/oversold conditions
        self.rsi = bt.indicators.RSI(period=14)
        
        self.logger.info(f"Breakout indicators initialized: "
                        f"lookback={self.params.lookback_period}, "
                        f"volume_filter={self.params.use_volume_filter}")
    
    def setup_signals(self):
        """Setup breakout signals."""
        # Donchian breakout signals
        self.donchian_high_break = self.data.close > self.donchian.dch
        self.donchian_low_break = self.data.close < self.donchian.dcl
        
        self.logger.debug("Breakout signals initialized")
    
    def next(self):
        """Override next to update support/resistance levels."""
        # Update support/resistance levels periodically
        if len(self.data) - self.last_update_bar >= 5:  # Update every 5 bars
            self._update_support_resistance_levels()
            self.last_update_bar = len(self.data)
        
        # Call parent next method
        super().next()
    
    def get_entry_signal(self) -> Optional[str]:
        """
        Determine entry signal based on support/resistance breakouts.
        
        Returns:
            'LONG' for resistance breakout, 'SHORT' for support breakdown, None otherwise
        """
        # Check if we have enough data
        if len(self.data) < self.params.lookback_period:
            return None
        
        current_price = self.data.close[0]
        
        signal = None
        
        # Check for resistance breakout (long signal)
        resistance_level = self._find_nearest_resistance(current_price)
        if resistance_level and self._check_resistance_breakout(current_price, resistance_level):
            if self._validate_long_breakout():
                signal = 'LONG'
                self.logger.info(f"Resistance breakout: Price={current_price:.5f}, "
                               f"Resistance={resistance_level[0]:.5f}")
        
        # Check for support breakdown (short signal)
        support_level = self._find_nearest_support(current_price)
        if support_level and self._check_support_breakdown(current_price, support_level):
            if self._validate_short_breakout():
                signal = 'SHORT'
                self.logger.info(f"Support breakdown: Price={current_price:.5f}, "
                                f"Support={support_level[0]:.5f}")
        
        return signal
    
    def _update_support_resistance_levels(self):
        """Update support and resistance levels based on recent price action."""
        if len(self.data) < self.params.lookback_period:
            return
        
        # Get recent highs and lows
        recent_highs = []
        recent_lows = []
        
        for i in range(self.params.lookback_period):
            high = self.data.high.get(ago=i)
            low = self.data.low.get(ago=i)
            recent_highs.append(high)
            recent_lows.append(low)
        
        # Find potential resistance levels (local highs)
        new_resistance_levels = self._find_local_extremes(recent_highs, 'high')
        
        # Find potential support levels (local lows)
        new_support_levels = self._find_local_extremes(recent_lows, 'low')
        
        # Update resistance levels
        self._update_levels(self.resistance_levels, new_resistance_levels)
        
        # Update support levels
        self._update_levels(self.support_levels, new_support_levels)
        
        # Age existing levels and remove old ones
        self._age_and_clean_levels()
    
    def _find_local_extremes(self, prices: List[float], extreme_type: str) -> List[Tuple[float, int, int]]:
        """Find local extremes (highs or lows) in price data."""
        extremes = []
        
        for i in range(2, len(prices) - 2):
            current_price = prices[i]
            
            if extreme_type == 'high':
                # Check if it's a local high
                if (current_price > prices[i-1] and current_price > prices[i-2] and
                    current_price > prices[i+1] and current_price > prices[i+2]):
                    
                    # Count how many times this level was tested
                    touches = self._count_level_touches(prices, current_price, 0.0001)
                    if touches >= self.params.min_touches:
                        extremes.append((current_price, touches, 0))  # (price, strength, age)
            
            else:  # extreme_type == 'low'
                # Check if it's a local low
                if (current_price < prices[i-1] and current_price < prices[i-2] and
                    current_price < prices[i+1] and current_price < prices[i+2]):
                    
                    # Count how many times this level was tested
                    touches = self._count_level_touches(prices, current_price, 0.0001)
                    if touches >= self.params.min_touches:
                        extremes.append((current_price, touches, 0))  # (price, strength, age)
        
        return extremes
    
    def _count_level_touches(self, prices: List[float], level: float, tolerance: float) -> int:
        """Count how many times a price level was touched."""
        touches = 0
        for price in prices:
            if abs(price - level) <= tolerance:
                touches += 1
        return touches
    
    def _update_levels(self, existing_levels: List, new_levels: List):
        """Update existing levels with new ones."""
        for new_level in new_levels:
            # Check if this level already exists
            level_exists = False
            for i, existing_level in enumerate(existing_levels):
                if abs(existing_level[0] - new_level[0]) < 0.0001:  # Same level
                    # Update strength if new level is stronger
                    if new_level[1] > existing_level[1]:
                        existing_levels[i] = (existing_level[0], new_level[1], existing_level[2])
                    level_exists = True
                    break
            
            if not level_exists:
                existing_levels.append(new_level)
    
    def _age_and_clean_levels(self):
        """Age existing levels and remove old ones."""
        # Age all levels
        for i in range(len(self.resistance_levels)):
            price, strength, age = self.resistance_levels[i]
            self.resistance_levels[i] = (price, strength, age + 1)
        
        for i in range(len(self.support_levels)):
            price, strength, age = self.support_levels[i]
            self.support_levels[i] = (price, strength, age + 1)
        
        # Remove old levels
        self.resistance_levels = [level for level in self.resistance_levels 
                                 if level[2] < self.params.max_level_age]
        self.support_levels = [level for level in self.support_levels 
                              if level[2] < self.params.max_level_age]
    
    def _find_nearest_resistance(self, current_price: float) -> Optional[Tuple[float, int, int]]:
        """Find the nearest resistance level above current price."""
        nearest_resistance = None
        min_distance = float('inf')
        
        for level in self.resistance_levels:
            price, strength, age = level
            if price > current_price:  # Above current price
                distance = price - current_price
                if distance < min_distance:
                    min_distance = distance
                    nearest_resistance = level
        
        return nearest_resistance
    
    def _find_nearest_support(self, current_price: float) -> Optional[Tuple[float, int, int]]:
        """Find the nearest support level below current price."""
        nearest_support = None
        min_distance = float('inf')
        
        for level in self.support_levels:
            price, strength, age = level
            if price < current_price:  # Below current price
                distance = current_price - price
                if distance < min_distance:
                    min_distance = distance
                    nearest_support = level
        
        return nearest_support
    
    def _check_resistance_breakout(self, current_price: float, resistance_level: Tuple[float, int, int]) -> bool:
        """Check if current price breaks above resistance."""
        resistance_price = resistance_level[0]
        return current_price > resistance_price * (1 + self.params.breakout_threshold)
    
    def _check_support_breakdown(self, current_price: float, support_level: Tuple[float, int, int]) -> bool:
        """Check if current price breaks below support."""
        support_price = support_level[0]
        return current_price < support_price * (1 - self.params.breakout_threshold)
    
    def _validate_long_breakout(self) -> bool:
        """Validate long breakout signal."""
        # Volume confirmation
        if self.params.use_volume_filter and hasattr(self, 'volume_ratio'):
            if self.volume_ratio[0] < self.params.volume_multiplier:
                self.logger.debug(f"Long breakout rejected: Insufficient volume "
                                f"({self.volume_ratio[0]:.2f})")
                return False
        
        # Momentum confirmation
        if self.momentum[0] <= 0:
            self.logger.debug("Long breakout rejected: Negative momentum")
            return False
        
        # RSI should not be extremely overbought
        if self.rsi[0] > 80:
            self.logger.debug(f"Long breakout rejected: RSI overbought ({self.rsi[0]:.2f})")
            return False
        
        # Donchian channel confirmation
        if not self.donchian_high_break[0]:
            self.logger.debug("Long breakout rejected: No Donchian high break")
            return False
        
        return True
    
    def _validate_short_breakout(self) -> bool:
        """Validate short breakout signal."""
        # Volume confirmation
        if self.params.use_volume_filter and hasattr(self, 'volume_ratio'):
            if self.volume_ratio[0] < self.params.volume_multiplier:
                self.logger.debug(f"Short breakout rejected: Insufficient volume "
                                f"({self.volume_ratio[0]:.2f})")
                return False
        
        # Momentum confirmation
        if self.momentum[0] >= 0:
            self.logger.debug("Short breakout rejected: Positive momentum")
            return False
        
        # RSI should not be extremely oversold
        if self.rsi[0] < 20:
            self.logger.debug(f"Short breakout rejected: RSI oversold ({self.rsi[0]:.2f})")
            return False
        
        # Donchian channel confirmation
        if not self.donchian_low_break[0]:
            self.logger.debug("Short breakout rejected: No Donchian low break")
            return False
        
        return True
    
    def get_strategy_description(self) -> str:
        """Get a description of the strategy."""
        return (f"Support/Resistance Breakout Strategy with "
                f"{self.params.lookback_period}-period lookback and "
                f"{'volume' if self.params.use_volume_filter else 'no volume'} confirmation")
    
    def get_current_indicators(self) -> dict:
        """Get current indicator values for analysis."""
        if len(self.data) < self.params.lookback_period:
            return {}
        
        indicators = {
            'momentum': self.momentum[0],
            'rsi': self.rsi[0],
            'donchian_high': self.donchian.dch[0],
            'donchian_low': self.donchian.dcl[0],
            'atr': self.atr[0],
            'support_levels': len(self.support_levels),
            'resistance_levels': len(self.resistance_levels),
        }
        
        if self.params.use_volume_filter and hasattr(self, 'volume_ratio'):
            indicators['volume_ratio'] = self.volume_ratio[0]
        
        # Add nearest levels
        current_price = self.data.close[0]
        nearest_support = self._find_nearest_support(current_price)
        nearest_resistance = self._find_nearest_resistance(current_price)
        
        if nearest_support:
            indicators['nearest_support'] = nearest_support[0]
        if nearest_resistance:
            indicators['nearest_resistance'] = nearest_resistance[0]
        
        return indicators
