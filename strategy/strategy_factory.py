"""
Strategy factory system for easy strategy selection and management.
Provides a registry pattern for adding and selecting trading strategies.
"""

import logging
from typing import Dict, Type, Any, List
from .base_strategy import BaseStrategy


class StrategyRegistry:
    """Registry for managing trading strategies."""
    
    def __init__(self):
        """Initialize the strategy registry."""
        self._strategies: Dict[str, Type[BaseStrategy]] = {}
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def register(self, name: str, strategy_class: Type) -> None:
        """
        Register a strategy class.

        Args:
            name: Strategy name identifier
            strategy_class: Strategy class (should inherit from bt.Strategy)
        """
        # Check if it's a Backtrader strategy (more flexible)
        try:
            import backtrader as bt
            if not issubclass(strategy_class, bt.Strategy):
                raise ValueError(f"Strategy class {strategy_class.__name__} must inherit from bt.Strategy")
        except Exception:
            # If we can't check, just log a warning
            self.logger.warning(f"Could not validate strategy class {strategy_class.__name__}")

        if name in self._strategies:
            self.logger.warning(f"Strategy '{name}' is being overridden")

        self._strategies[name] = strategy_class
        self.logger.info(f"Registered strategy: {name} -> {strategy_class.__name__}")
    
    def get_strategy(self, name: str) -> Type[BaseStrategy]:
        """
        Get a strategy class by name.
        
        Args:
            name: Strategy name identifier
            
        Returns:
            Strategy class
            
        Raises:
            ValueError: If strategy name is not found
        """
        if name not in self._strategies:
            available = list(self._strategies.keys())
            raise ValueError(f"Unknown strategy: '{name}'. Available strategies: {available}")
        
        return self._strategies[name]
    
    def list_strategies(self) -> List[str]:
        """
        Get list of available strategy names.
        
        Returns:
            List of strategy names
        """
        return list(self._strategies.keys())
    
    def get_strategy_info(self, name: str) -> Dict[str, Any]:
        """
        Get information about a strategy.
        
        Args:
            name: Strategy name identifier
            
        Returns:
            Dictionary with strategy information
        """
        if name not in self._strategies:
            raise ValueError(f"Unknown strategy: '{name}'")
        
        strategy_class = self._strategies[name]
        
        return {
            'name': name,
            'class_name': strategy_class.__name__,
            'module': strategy_class.__module__,
            'docstring': strategy_class.__doc__,
            'parameters': getattr(strategy_class, 'params', ()),
        }
    
    def create_strategy(self, name: str, **kwargs) -> BaseStrategy:
        """
        Create a strategy instance.
        
        Args:
            name: Strategy name identifier
            **kwargs: Additional parameters to pass to strategy constructor
            
        Returns:
            Strategy instance
        """
        strategy_class = self.get_strategy(name)
        
        # Update strategy parameters if provided
        if kwargs:
            # Create a new params tuple with updated values
            original_params = getattr(strategy_class, 'params', ())
            param_dict = dict(original_params)
            param_dict.update(kwargs)
            
            # Create a new class with updated parameters
            class ConfiguredStrategy(strategy_class):
                params = tuple(param_dict.items())
            
            return ConfiguredStrategy
        
        return strategy_class


# Global strategy registry instance
_registry = StrategyRegistry()


def register_strategy(name: str, strategy_class: Type[BaseStrategy]) -> None:
    """
    Register a strategy class in the global registry.
    
    Args:
        name: Strategy name identifier
        strategy_class: Strategy class that inherits from BaseStrategy
    """
    _registry.register(name, strategy_class)


def get_strategy(name: str) -> Type[BaseStrategy]:
    """
    Get a strategy class by name from the global registry.
    
    Args:
        name: Strategy name identifier
        
    Returns:
        Strategy class
    """
    return _registry.get_strategy(name)


def list_strategies() -> List[str]:
    """
    Get list of available strategy names from the global registry.
    
    Returns:
        List of strategy names
    """
    return _registry.list_strategies()


def get_strategy_info(name: str) -> Dict[str, Any]:
    """
    Get information about a strategy from the global registry.
    
    Args:
        name: Strategy name identifier
        
    Returns:
        Dictionary with strategy information
    """
    return _registry.get_strategy_info(name)


def create_strategy(name: str, **kwargs) -> BaseStrategy:
    """
    Create a strategy instance from the global registry.
    
    Args:
        name: Strategy name identifier
        **kwargs: Additional parameters to pass to strategy constructor
        
    Returns:
        Strategy instance
    """
    return _registry.create_strategy(name, **kwargs)


def strategy_decorator(name: str):
    """
    Decorator for automatically registering strategies.
    
    Args:
        name: Strategy name identifier
        
    Example:
        @strategy_decorator('my_strategy')
        class MyStrategy(BaseStrategy):
            pass
    """
    def decorator(strategy_class: Type[BaseStrategy]):
        register_strategy(name, strategy_class)
        return strategy_class
    return decorator


# Dictionary for backward compatibility
STRATEGY_REGISTRY = _registry._strategies


def initialize_default_strategies():
    """Initialize and register default strategies."""
    try:
        # Import and register minimal strategy (testing version)
        try:
            from .minimal_strategy import MinimalStrategy
            register_strategy('ema_crossover', MinimalStrategy)
            register_strategy('minimal', MinimalStrategy)
        except ImportError as e:
            logging.warning(f"Could not import Minimal strategy: {e}")

        # Import and register simple EMA strategy (working version)
        try:
            from .simple_ema_strategy import SimpleEMAStrategy
            register_strategy('simple_ema', SimpleEMAStrategy)
        except ImportError as e:
            logging.warning(f"Could not import Simple EMA strategy: {e}")

        # Import and register complex EMA crossover strategy
        try:
            from .ema_crossover_strategy import EMACrossoverStrategy
            register_strategy('ema_crossover_advanced', EMACrossoverStrategy)
        except ImportError as e:
            logging.warning(f"Could not import EMA crossover strategy: {e}")

        # Import and register additional strategies
        try:
            from .rsi_macd_strategy import RSIMACDStrategy
            register_strategy('rsi_macd', RSIMACDStrategy)
        except ImportError as e:
            logging.warning(f"Could not import RSI+MACD strategy: {e}")

        try:
            from .bollinger_bands_strategy import BollingerBandsStrategy
            register_strategy('bollinger_bands', BollingerBandsStrategy)
        except ImportError as e:
            logging.warning(f"Could not import Bollinger Bands strategy: {e}")

        try:
            from .breakout_strategy import BreakoutStrategy
            register_strategy('breakout', BreakoutStrategy)
        except ImportError as e:
            logging.warning(f"Could not import Breakout strategy: {e}")

        logging.info("Default strategies initialization completed")

    except Exception as e:
        logging.error(f"Error during strategy initialization: {e}")


# Auto-initialize default strategies when module is imported
try:
    initialize_default_strategies()
except Exception as e:
    logging.warning(f"Failed to initialize some default strategies: {e}")


def print_available_strategies():
    """Print information about all available strategies."""
    strategies = list_strategies()
    
    if not strategies:
        print("No strategies are currently registered.")
        return
    
    print("Available Trading Strategies:")
    print("=" * 50)
    
    for strategy_name in strategies:
        try:
            info = get_strategy_info(strategy_name)
            print(f"\nStrategy: {strategy_name}")
            print(f"Class: {info['class_name']}")
            if info['docstring']:
                print(f"Description: {info['docstring'].strip().split('.')[0]}")
            
            if info['parameters']:
                print("Parameters:")
                try:
                    for param_name, default_value in info['parameters']:
                        print(f"  - {param_name}: {default_value}")
                except (TypeError, ValueError):
                    print("  - Parameters available but format error")
        
        except Exception as e:
            print(f"\nStrategy: {strategy_name} (Error loading info: {e})")
    
    print("\n" + "=" * 50)
