"""
Configuration management module for the Forex backtesting system.
Handles loading and validation of configuration parameters from config.ini file.
"""

import configparser
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
import os


class ConfigManager:
    """Manages configuration settings for the backtesting system."""
    
    def __init__(self, config_file: str = "config.ini"):
        """
        Initialize the configuration manager.
        
        Args:
            config_file: Path to the configuration file
        """
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self._load_config()
        self._validate_config()
    
    def _load_config(self) -> None:
        """Load configuration from the config file."""
        if not os.path.exists(self.config_file):
            raise FileNotFoundError(f"Configuration file {self.config_file} not found")
        
        self.config.read(self.config_file)
        logging.info(f"Configuration loaded from {self.config_file}")
    
    def _validate_config(self) -> None:
        """Validate configuration parameters."""
        required_sections = ['STRATEGY', 'BACKTEST', 'RISK_MANAGEMENT', 'DATA']
        
        for section in required_sections:
            if not self.config.has_section(section):
                raise ValueError(f"Missing required configuration section: {section}")
        
        # Validate date format
        try:
            self.get_start_date()
            self.get_end_date()
        except ValueError as e:
            raise ValueError(f"Invalid date format in configuration: {e}")
        
        # Validate numeric parameters
        if self.get_initial_cash() <= 0:
            raise ValueError("Initial cash must be positive")
        
        if not 0 < self.get_position_size_percent() <= 1:
            raise ValueError("Position size percent must be between 0 and 1")
        
        if self.get_max_drawdown() <= 0 or self.get_max_drawdown() > 1:
            raise ValueError("Max drawdown must be between 0 and 1")
    
    # Strategy Configuration
    def get_strategy_name(self) -> str:
        """Get the strategy name."""
        return self.config.get('STRATEGY', 'strategy_name')
    
    def get_ema_fast(self) -> int:
        """Get the fast EMA period."""
        return self.config.getint('STRATEGY', 'ema_fast')
    
    def get_ema_slow(self) -> int:
        """Get the slow EMA period."""
        return self.config.getint('STRATEGY', 'ema_slow')
    
    def get_atr_period(self) -> int:
        """Get the ATR period."""
        return self.config.getint('STRATEGY', 'atr_period')
    
    def get_volatility_multiplier(self) -> float:
        """Get the volatility multiplier."""
        return self.config.getfloat('STRATEGY', 'volatility_multiplier')
    
    def get_max_positions(self) -> int:
        """Get the maximum number of positions."""
        return self.config.getint('STRATEGY', 'max_positions')
    
    def get_volatility_method(self) -> str:
        """Get the volatility calculation method."""
        return self.config.get('STRATEGY', 'volatility_method')
    
    # Backtest Configuration
    def get_initial_cash(self) -> float:
        """Get the initial cash amount."""
        return self.config.getfloat('BACKTEST', 'initial_cash')
    
    def get_commission(self) -> float:
        """Get the commission rate."""
        return self.config.getfloat('BACKTEST', 'commission')
    
    def get_slippage(self) -> float:
        """Get the slippage rate."""
        return self.config.getfloat('BACKTEST', 'slippage')
    
    def get_timeframe(self) -> str:
        """Get the timeframe."""
        return self.config.get('BACKTEST', 'timeframe')
    
    def get_start_date(self) -> datetime:
        """Get the backtest start date."""
        date_str = self.config.get('BACKTEST', 'start_date')
        return datetime.strptime(date_str, '%Y-%m-%d')
    
    def get_end_date(self) -> datetime:
        """Get the backtest end date."""
        date_str = self.config.get('BACKTEST', 'end_date')
        return datetime.strptime(date_str, '%Y-%m-%d')
    
    # Risk Management Configuration
    def get_max_drawdown(self) -> float:
        """Get the maximum drawdown threshold."""
        return self.config.getfloat('RISK_MANAGEMENT', 'max_drawdown')
    
    def get_position_size_percent(self) -> float:
        """Get the position size as percentage of account."""
        return self.config.getfloat('RISK_MANAGEMENT', 'position_size_percent')
    
    def get_martingale_multiplier(self) -> float:
        """Get the martingale multiplier."""
        return self.config.getfloat('RISK_MANAGEMENT', 'martingale_multiplier')
    
    def get_max_martingale_levels(self) -> int:
        """Get the maximum martingale levels."""
        return self.config.getint('RISK_MANAGEMENT', 'max_martingale_levels')
    
    # Data Configuration
    def get_currency_pairs(self) -> List[str]:
        """Get the list of currency pairs."""
        pairs_str = self.config.get('DATA', 'currency_pairs')
        return [pair.strip() for pair in pairs_str.split(',')]
    
    def get_data_source(self) -> str:
        """Get the data source."""
        return self.config.get('DATA', 'data_source')
    
    def get_validate_data(self) -> bool:
        """Get whether to validate data."""
        return self.config.getboolean('DATA', 'validate_data')
    
    # Reporting Configuration
    def get_generate_html_report(self) -> bool:
        """Get whether to generate HTML report."""
        return self.config.getboolean('REPORTING', 'generate_html_report')
    
    def get_include_charts(self) -> bool:
        """Get whether to include charts in report."""
        return self.config.getboolean('REPORTING', 'include_charts')
    
    def get_chart_theme(self) -> str:
        """Get the chart theme."""
        return self.config.get('REPORTING', 'chart_theme')
    
    def get_report_filename(self) -> str:
        """Get the report filename."""
        return self.config.get('REPORTING', 'report_filename')
    
    # Logging Configuration
    def get_log_level(self) -> str:
        """Get the logging level."""
        return self.config.get('LOGGING', 'log_level')
    
    def get_log_file(self) -> str:
        """Get the log file path."""
        return self.config.get('LOGGING', 'log_file')
    
    def get_console_output(self) -> bool:
        """Get whether to output to console."""
        return self.config.getboolean('LOGGING', 'console_output')
    
    def get_all_config(self) -> Dict[str, Any]:
        """Get all configuration as a dictionary."""
        config_dict = {}
        for section in self.config.sections():
            config_dict[section] = dict(self.config.items(section))
        return config_dict
    
    def update_config(self, section: str, key: str, value: str) -> None:
        """
        Update a configuration value.
        
        Args:
            section: Configuration section
            key: Configuration key
            value: New value
        """
        if not self.config.has_section(section):
            self.config.add_section(section)
        
        self.config.set(section, key, str(value))
        
        # Save to file
        with open(self.config_file, 'w') as f:
            self.config.write(f)
        
        logging.info(f"Updated configuration: {section}.{key} = {value}")


def setup_logging(config_manager: ConfigManager) -> None:
    """
    Setup logging configuration.
    
    Args:
        config_manager: Configuration manager instance
    """
    log_level = getattr(logging, config_manager.get_log_level().upper())
    log_file = config_manager.get_log_file()
    console_output = config_manager.get_console_output()
    
    # Create formatters
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Setup root logger
    logger = logging.getLogger()
    logger.setLevel(log_level)
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Add file handler
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    # Add console handler
    if console_output:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)


# Global configuration instance
config_manager = None

def get_config() -> ConfigManager:
    """Get the global configuration manager instance."""
    global config_manager
    if config_manager is None:
        config_manager = ConfigManager()
    return config_manager
